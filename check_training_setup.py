"""
检查训练环境设置
"""

import sys
import os
import traceback
from configparser import ConfigParser


def check_config_file():
    """检查配置文件"""
    config_file = 'configs/config_Maze_SimpleMultirotor_2D_stable.ini'
    
    try:
        if not os.path.exists(config_file):
            print(f"❌ Config file not found: {config_file}")
            return False
        
        cfg = ConfigParser()
        cfg.read(config_file, encoding='utf-8')
        
        # 检查必要的sections
        required_sections = ['options', 'environment', 'DRL']
        for section in required_sections:
            if not cfg.has_section(section):
                print(f"❌ Missing section '{section}' in config file")
                return False
        
        # 检查关键配置
        algo = cfg.get('options', 'algo')
        env_name = cfg.get('options', 'env_name')
        dynamic_name = cfg.get('options', 'dynamic_name')
        policy_name = cfg.get('options', 'policy_name')
        
        print(f"✅ Config file valid:")
        print(f"   Algorithm: {algo}")
        print(f"   Environment: {env_name}")
        print(f"   Dynamics: {dynamic_name}")
        print(f"   Policy: {policy_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config file error: {str(e)}")
        return False


def check_python_environment():
    """检查Python环境"""
    try:
        import torch
        import numpy
        import gym
        import airsim
        import cv2
        from PyQt5 import QtWidgets
        
        print("✅ Python environment:")
        print(f"   PyTorch: {torch.__version__}")
        print(f"   NumPy: {numpy.__version__}")
        print(f"   OpenAI Gym: {gym.__version__}")
        print(f"   OpenCV: {cv2.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing Python package: {str(e)}")
        return False


def check_stable_baselines3():
    """检查Stable Baselines3"""
    try:
        sys.path.append('stable-baselines3')
        from stable_baselines3 import SAC, TD3, PPO
        
        print("✅ Stable Baselines3 available")
        return True
        
    except ImportError as e:
        print(f"❌ Stable Baselines3 import error: {str(e)}")
        return False


def check_custom_modules():
    """检查自定义模块"""
    try:
        # 检查gym_env
        sys.path.append('gym_env')
        import gym_env
        
        # 检查utils
        sys.path.append('scripts/utils')
        from airsim_stability_manager import AirSimStabilityManager
        from airsim_recovery_manager import AirSimRecoveryManager
        
        print("✅ Custom modules:")
        print("   gym_env: Available")
        print("   AirSimStabilityManager: Available")
        print("   AirSimRecoveryManager: Available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Custom module import error: {str(e)}")
        return False


def check_directories():
    """检查目录结构"""
    required_dirs = [
        'configs',
        'scripts',
        'scripts/utils',
        'gym_env',
        'stable-baselines3',
        'logs'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    else:
        print("✅ Directory structure complete")
        return True


def check_airsim_connection():
    """检查AirSim连接"""
    try:
        import airsim
        client = airsim.MultirotorClient()
        client.confirmConnection()
        
        # 测试基本功能
        client.ping()
        
        print("✅ AirSim connection successful")
        return True
        
    except Exception as e:
        print(f"❌ AirSim connection failed: {str(e)}")
        print("   Make sure AirSim is running with the correct environment")
        return False


def main():
    """主检查函数"""
    print("🔧 Checking Training Setup")
    print("=" * 50)
    
    checks = [
        ("Directory Structure", check_directories),
        ("Config File", check_config_file),
        ("Python Environment", check_python_environment),
        ("Stable Baselines3", check_stable_baselines3),
        ("Custom Modules", check_custom_modules),
        ("AirSim Connection", check_airsim_connection),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}...")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check failed with exception: {str(e)}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 Setup Check Summary:")
    
    all_passed = True
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {check_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All checks passed! You're ready to start training.")
        print("\n🚀 Run training with:")
        print("   python scripts/start_train_with_plot_stable.py")
    else:
        print("\n⚠️  Some checks failed. Please fix the issues above before training.")
        
        print("\n🔧 Common Solutions:")
        print("   - Install missing Python packages: pip install -r requirements.txt")
        print("   - Make sure AirSim is running")
        print("   - Check file paths and directory structure")
        print("   - Verify configuration files")


if __name__ == "__main__":
    main()
