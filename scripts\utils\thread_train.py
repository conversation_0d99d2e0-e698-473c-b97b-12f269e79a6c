from .custom_policy_sb3 import CNN_FC, CNN_GAP, CNN_GAP_BN, No_CNN, CNN_MobileNet, CNN_GAP_new, No_CNN_64, No_CNN_100, No_CNN_mix
import datetime
import gym
import gym_env
import numpy as np
from stable_baselines3 import TD3, PPO, SAC
from stable_baselines3.common.noise import NormalActionNoise
from wandb.integration.sb3 import WandbCallback
import wandb
from PyQt5 import QtCore
import argparse
import ast
from configparser import ConfigParser
import torch as th
import os
import sys
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(CURRENT_DIR))


def get_parser():
    parser = argparse.ArgumentParser(
        description="Training thread without plot")
    parser.add_argument(
        '-c',
        '--config',
        help='config file name in configs folder, such as config_default',
        default='config_Trees_SimpleMultirotor_2D')
    parser.add_argument('-n',
                        '--note',
                        help='training objective',
                        default='depth_upper_split_5')

    return parser


class TrainingThread(QtCore.QThread):
    """
    QT thread for policy training
    """

    def __init__(self, config):
        super(TrainingThread, self).__init__()
        print("init training thread")

        # config
        self.cfg = ConfigParser()

        # 尝试使用不同的编码读取配置文件
        try:
            self.cfg.read(config, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                self.cfg.read(config)
            except Exception as e:
                raise ValueError(f"Cannot read config file {config}: {e}")

        # 验证必要的section是否存在
        if not self.cfg.has_section('options'):
            raise ValueError(f"Missing [options] section in config file: {config}")

        env_name = self.cfg.get('options', 'env_name')
        self.project_name = env_name

        # make gym environment
        self.env = gym.make('airsim-env-v0')
        self.env.set_config(self.cfg)

        wandb_name = self.cfg.get(
            'options', 'policy_name') + '-' + self.cfg.get('options', 'algo')
        if self.cfg.get('options', 'dynamic_name') == 'SimpleFixedwing':
            if self.cfg.get('options', 'perception') == "lgmd":
                wandb_name += '-LGMD'
            else:
                wandb_name += '-depth'
            if self.cfg.getfloat('fixedwing', 'pitch_flap_hz') != 0:
                wandb_name += '-Flapping'

        # wandb
        if self.cfg.getboolean('options', 'use_wandb'):
            wandb.init(
                project=self.project_name,
                notes=self.cfg.get('wandb', 'notes'),
                name=self.cfg.get('wandb', 'name'),
                sync_tensorboard=True,  # auto-upload sb3's tensorboard metrics
                save_code=True,  # optional
            )

    def terminate(self):
        print('TrainingThread terminated')

    def run(self):
        print("run training thread")

        # ! -----------------------------------init folders-----------------------------------------
        now = datetime.datetime.now()
        now_string = now.strftime('%Y_%m_%d_%H_%M')
        file_path = 'logs/' + self.project_name + '/' + now_string + '_' + self.cfg.get(
            'options', 'dynamic_name') + '_' + self.cfg.get(
                'options', 'policy_name') + '_' + self.cfg.get(
                    'options', 'algo')
        log_path = file_path + '/tb_logs'
        model_path = file_path + '/models'
        config_path = file_path + '/config'
        data_path = file_path + '/data'
        os.makedirs(log_path, exist_ok=True)
        os.makedirs(model_path, exist_ok=True)
        os.makedirs(config_path, exist_ok=True)
        os.makedirs(data_path, exist_ok=True)  # create data path to save q_map

        # save config file
        with open(config_path + '\config.ini', 'w') as configfile:
            self.cfg.write(configfile)

        #! -----------------------------------policy selection-------------------------------------
        feature_num_state = self.env.dynamic_model.state_feature_length
        feature_num_cnn = self.cfg.getint('options', 'cnn_feature_num')
        policy_name = self.cfg.get('options', 'policy_name')

        # feature extraction network
        if self.cfg.get('options', 'activation_function') == 'tanh':
            activation_function = th.nn.Tanh
        else:
            activation_function = th.nn.ReLU
        
        if policy_name == 'mlp':
            policy_base = 'MlpPolicy'
            policy_kwargs = dict(activation_fn=activation_function)
        else:
            policy_base = 'CnnPolicy'
            if policy_name == 'CNN_FC':
                policy_used = CNN_FC
            elif policy_name == 'CNN_GAP':
                policy_used = CNN_GAP_new
                #policy_used = CNN_GAP
            elif policy_name == 'CNN_GAP_BN':
                policy_used = CNN_GAP_BN
            elif policy_name == 'CNN_MobileNet':
                policy_used = CNN_MobileNet
            elif policy_name == 'No_CNN':
                policy_used = No_CNN
            elif policy_name == 'No_CNN_64':
                policy_used = No_CNN_64
            elif policy_name == 'No_CNN_100':
                policy_used = No_CNN_100
            elif policy_name == 'No_CNN_mix':
                policy_used = No_CNN_mix
            else:
                raise Exception('policy select error: ', policy_name)

            policy_kwargs = dict(
                features_extractor_class=policy_used,
                features_extractor_kwargs=dict(
                    features_dim=feature_num_state + feature_num_cnn,
                    state_feature_dim=feature_num_state),
                activation_fn=activation_function)

        # fully-connected work after feature extraction
        net_arch_list = ast.literal_eval(self.cfg.get("options", "net_arch"))
        policy_kwargs['net_arch'] = net_arch_list

        #! ---------------------------------algorithm selection-------------------------------------
        algo = self.cfg.get('options', 'algo')
        print('algo: ', algo)
        if algo == 'PPO':
            model = PPO(
                policy_base,
                self.env,
                # n_steps = 200,
                learning_rate=self.cfg.getfloat('DRL', 'learning_rate'),
                policy_kwargs=policy_kwargs,
                tensorboard_log=log_path,
                seed=0,
                verbose=2)
        elif algo == 'SAC':
            n_actions = self.env.action_space.shape[-1]
            noise_sigma = self.cfg.getfloat(
                'DRL', 'action_noise_sigma') * np.ones(n_actions)
            action_noise = NormalActionNoise(mean=np.zeros(n_actions),
                                             sigma=noise_sigma)
            model = SAC(
                policy_base,
                self.env,
                action_noise=action_noise,
                policy_kwargs=policy_kwargs,
                buffer_size=self.cfg.getint('DRL', 'buffer_size'),
                gamma=self.cfg.getfloat('DRL', 'gamma'),
                learning_starts=self.cfg.getint('DRL', 'learning_starts'),
                learning_rate=self.cfg.getfloat('DRL', 'learning_rate'),
                batch_size=self.cfg.getint('DRL', 'batch_size'),
                train_freq=(self.cfg.getint('DRL', 'train_freq'), 'step'),
                gradient_steps=self.cfg.getint('DRL', 'gradient_steps'),
                tensorboard_log=log_path,
                seed=0,
                verbose=2)
        elif algo == 'TD3':
            # The noise objects for TD3
            n_actions = self.env.action_space.shape[-1]
            noise_sigma = self.cfg.getfloat(
                'DRL', 'action_noise_sigma') * np.ones(n_actions)
            action_noise = NormalActionNoise(mean=np.zeros(n_actions),
                                             sigma=noise_sigma)
            model = TD3(
                policy_base,
                self.env,
                action_noise=action_noise,
                learning_rate=self.cfg.getfloat('DRL', 'learning_rate'),
                gamma=self.cfg.getfloat('DRL', 'gamma'),
                policy_kwargs=policy_kwargs,
                learning_starts=self.cfg.getint('DRL', 'learning_starts'),
                batch_size=self.cfg.getint('DRL', 'batch_size'),
                train_freq=(self.cfg.getint('DRL', 'train_freq'), 'step'),
                gradient_steps=self.cfg.getint('DRL', 'gradient_steps'),
                buffer_size=self.cfg.getint('DRL', 'buffer_size'),
                tensorboard_log=log_path,
                seed=0,
                verbose=2)
        else:
            raise Exception('Invalid algo name : ', algo)

        # TODO create eval_callback
        # eval_freq = self.cfg.getint('TD3', 'eval_freq')
        # n_eval_episodes = self.cfg.getint('TD3', 'n_eval_episodes')
        # eval_callback = EvalCallback(self.env, best_model_save_path= file_path + '/eval',
        #                      log_path= file_path + '/eval', eval_freq=eval_freq, n_eval_episodes=n_eval_episodes,
        #                      deterministic=True, render=False)

        #! -------------------------------------train-----------------------------------------
        print('start training model')
        total_timesteps = self.cfg.getint('options', 'total_timesteps')
        self.env.model = model
        self.env.data_path = data_path

        # 创建检查点保存回调和早停回调
        from stable_baselines3.common.callbacks import CallbackList, BaseCallback

        # 检查点保存路径
        checkpoint_path = model_path + '/checkpoints'
        os.makedirs(checkpoint_path, exist_ok=True)

        # 创建自定义早停和检查点管理回调
        class EarlyStoppingCheckpointCallback(BaseCallback):
            def __init__(self, save_freq=5000, checkpoint_path=None, model_path=None,
                         early_stop_check_freq=50000, patience_threshold=0.0, verbose=1):
                super(EarlyStoppingCheckpointCallback, self).__init__(verbose)
                self.save_freq = save_freq
                self.checkpoint_path = checkpoint_path
                self.model_path = model_path
                self.early_stop_check_freq = early_stop_check_freq
                self.patience_threshold = patience_threshold

                # 跟踪成功率历史（基于训练过程中的实际数据）
                self.checkpoint_success_rates = {}  # 存储每个检查点的成功率
                self.best_success_rate = -1.0
                self.best_checkpoint_path = None
                self.last_check_step = 0

            def _on_step(self) -> bool:
                # 每save_freq步保存检查点
                if self.n_calls % self.save_freq == 0:
                    checkpoint_name = f"checkpoint_{self.n_calls}_steps"
                    checkpoint_file = os.path.join(self.checkpoint_path, f"{checkpoint_name}.zip")
                    self.model.save(checkpoint_file)

                    # 获取当前模型的成功率（从训练过程中的统计数据）
                    success_rate = self._get_current_success_rate()
                    self.checkpoint_success_rates[self.n_calls] = success_rate

                    if self.verbose > 0:
                        print(f"Step {self.n_calls}: Checkpoint saved, Success rate: {success_rate:.3f}")

                    # 更新最佳检查点
                    if success_rate > self.best_success_rate:
                        self.best_success_rate = success_rate
                        self.best_checkpoint_path = checkpoint_file
                        if self.verbose > 0:
                            print(f"New best checkpoint! Success rate: {success_rate:.3f}")

                # 每early_stop_check_freq步检查是否需要早停
                if (self.n_calls - self.last_check_step) >= self.early_stop_check_freq and self.n_calls > self.early_stop_check_freq:
                    current_success_rate = self._get_current_success_rate()

                    # 获取前一个周期的成功率
                    previous_step = self.n_calls - self.early_stop_check_freq
                    previous_success_rate = None

                    # 找到最接近previous_step的检查点成功率
                    for step in sorted(self.checkpoint_success_rates.keys(), reverse=True):
                        if step <= previous_step:
                            previous_success_rate = self.checkpoint_success_rates[step]
                            break

                    if previous_success_rate is not None:
                        improvement = current_success_rate - previous_success_rate

                        if self.verbose > 0:
                            print(f"Early stopping check at step {self.n_calls}:")
                            print(f"  Current success rate: {current_success_rate:.3f}")
                            print(f"  Previous success rate: {previous_success_rate:.3f}")
                            print(f"  Improvement: {improvement:.3f}")

                        # 如果成功率下降，触发早停
                        if improvement < self.patience_threshold:
                            print(f"Early stopping triggered! No improvement in success rate.")
                            print(f"Best checkpoint: {self.best_checkpoint_path} (Success rate: {self.best_success_rate:.3f})")
                            return False  # 停止训练

                    self.last_check_step = self.n_calls

                return True

            def _get_current_success_rate(self):
                """从训练过程中的统计数据获取当前成功率"""
                try:
                    # 尝试从模型的成功率缓冲区获取最近的成功率
                    if hasattr(self.model, 'ep_success_buffer') and len(self.model.ep_success_buffer) > 0:
                        # 使用最近100个episode的成功率（与训练日志中显示的success_rate一致）
                        from stable_baselines3.common.utils import safe_mean
                        success_rate = safe_mean(self.model.ep_success_buffer)
                        return success_rate
                    else:
                        # 如果没有成功率数据，返回0
                        if self.verbose > 0:
                            print("Warning: No success rate data available, using 0.0")
                        return 0.0
                except Exception as e:
                    if self.verbose > 0:
                        print(f"Warning: Could not get success rate from model buffer: {e}")
                    return 0.0

            def _on_training_end(self):
                """训练结束时保存最佳模型"""
                if self.best_checkpoint_path and os.path.exists(self.best_checkpoint_path):
                    final_model_path = os.path.join(self.model_path, 'model_sb3.zip')

                    # 复制最佳检查点为最终模型
                    import shutil
                    shutil.copy2(self.best_checkpoint_path, final_model_path)

                    if self.verbose > 0:
                        print(f"Best model saved as final model: {final_model_path}")
                        print(f"Best success rate: {self.best_success_rate:.3f}")
                else:
                    # 如果没有最佳检查点，保存当前模型
                    final_model_path = os.path.join(self.model_path, 'model_sb3.zip')
                    self.model.save(final_model_path)
                    if self.verbose > 0:
                        print(f"Current model saved as final model: {final_model_path}")

        # 创建增强的检查点回调，每5000步保存一次
        enhanced_callback = EarlyStoppingCheckpointCallback(
            save_freq=5000,
            checkpoint_path=checkpoint_path,
            model_path=model_path,
            early_stop_check_freq=50000,
            patience_threshold=0.0,  # 成功率不能下降
            verbose=1
        )

        if self.cfg.getboolean('options', 'use_wandb'):
            # if algo == 'TD3' or algo == 'SAC':
            #     wandb.watch(model.actor, log_freq=100, log="all")  # log gradients
            # elif algo == 'PPO':
            #     wandb.watch(model.policy, log_freq=100, log="all")

            # 组合WandB回调和增强检查点回调
            wandb_callback = WandbCallback(
                model_save_freq=10000,
                gradient_save_freq=5000,
                model_save_path=model_path,
                verbose=2,
            )

            # 使用CallbackList组合多个回调
            callback_list = CallbackList([enhanced_callback, wandb_callback])

            model.learn(
                total_timesteps,
                log_interval=1,
                callback=callback_list
            )
        else:
            # 只使用增强检查点回调
            model.learn(
                total_timesteps,
                log_interval=1,
                callback=enhanced_callback
            )

        #! ---------------------------model save----------------------------------------------------
        # 注意：最终模型的保存现在由EarlyStoppingCheckpointCallback处理
        # 它会自动保存成功率最高的检查点作为最终的model_sb3.zip

        print('training finished')
        print('model saved to: {}'.format(model_path))


def main():
    parser = get_parser()
    args = parser.parse_args()

    config_file = 'configs/' + args.config + '.ini'

    print(config_file)

    training_thread = TrainingThread(config_file)
    training_thread.run()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print('system exit')
