#!/usr/bin/env python3
"""
目标检测模块安装脚本
安装YOLO v5和其他必要依赖
"""

import subprocess
import sys
import os


def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package}")
        return False


def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False


def main():
    """主安装函数"""
    print("="*50)
    print("目标检测模块依赖安装")
    print("="*50)
    
    # 需要安装的包列表
    packages = [
        "yolov5",
        "torch",
        "torchvision", 
        "opencv-python",
        "Pillow",
        "matplotlib",
        "seaborn"
    ]
    
    print("检查现有依赖...")
    
    # 检查已安装的包
    installed = []
    to_install = []
    
    for package in packages:
        if package == "opencv-python":
            check_name = "cv2"
        elif package == "Pillow":
            check_name = "PIL"
        else:
            check_name = package
            
        if check_package(check_name):
            print(f"✓ {package} 已安装")
            installed.append(package)
        else:
            print(f"✗ {package} 未安装")
            to_install.append(package)
    
    if not to_install:
        print("\n所有依赖已安装完成！")
        return
    
    print(f"\n需要安装 {len(to_install)} 个包...")
    
    # 安装缺失的包
    failed = []
    for package in to_install:
        print(f"\n正在安装 {package}...")
        if not install_package(package):
            failed.append(package)
    
    # 安装结果总结
    print("\n" + "="*50)
    print("安装结果总结")
    print("="*50)
    
    if failed:
        print(f"✗ 安装失败的包: {', '.join(failed)}")
        print("\n请手动安装失败的包:")
        for package in failed:
            print(f"  pip install {package}")
    else:
        print("✓ 所有依赖安装成功！")
    
    # 验证YOLO安装
    print("\n验证YOLO v5安装...")
    try:
        import yolov5
        model = yolov5.load('yolov5n.pt')
        print("✓ YOLO v5安装验证成功！")
    except Exception as e:
        print(f"✗ YOLO v5验证失败: {e}")
        print("请检查网络连接，YOLO首次使用需要下载预训练模型")
    
    print("\n安装完成！现在可以使用目标检测功能了。")
    print("使用方法:")
    print("1. 运行主评估脚本: python scripts/start_evaluate_with_plot.py")
    print("2. 运行独立测试: python scripts/test_target_detection.py")
    print("3. 查看训练指南: YOLO_Training_Guide.md")


if __name__ == "__main__":
    main()
