"""
稳定版继续训练脚本 - 集成了恢复机制和兼容性修复
"""

import sys
import argparse
import os
import glob
import time
import traceback
from datetime import datetime

from utils.thread_train import TrainingThread
from configparser import ConfigParser

# 导入stable-baselines3
from stable_baselines3 import SAC, TD3, PP<PERSON>


def get_parser():
    parser = argparse.ArgumentParser(
        description="Continue training from saved model with stability enhancements")
    parser.add_argument('-config', required=False,
                        help='config file name', 
                        default='config_Maze_SimpleMultirotor_2D_stable')
    parser.add_argument('-model_path', required=False,
                        help='path to saved model (if not provided, will find latest)')
    parser.add_argument('-additional_steps', type=int, default=50000,
                        help='additional training steps')
    return parser


def find_best_model():
    """查找最佳模型（优先选择最终模型model_sb3.zip，如果不存在则选择最新检查点）"""
    # 尝试多个可能的logs目录位置
    possible_logs_dirs = [
        'logs',     # 从项目根目录运行
        '../logs',  # 从scripts目录运行
        os.path.join(os.path.dirname(__file__), '..', 'logs')  # 相对于脚本位置
    ]
    
    logs_dir = None
    for dir_path in possible_logs_dirs:
        if os.path.exists(dir_path):
            logs_dir = dir_path
            break
    
    if logs_dir is None:
        print("No logs directory found in any of these locations:")
        for dir_path in possible_logs_dirs:
            print(f"  - {os.path.abspath(dir_path)}")
        return None, None
    
    print(f"Using logs directory: {os.path.abspath(logs_dir)}")

    # 首先查找最终模型（这些通常是训练完成后保存的最佳模型）
    final_model_pattern = os.path.join(logs_dir, '**/models/model_sb3.zip')
    final_models = glob.glob(final_model_pattern, recursive=True)

    if final_models:
        # 如果有多个最终模型，选择最新的
        latest_final_model = max(final_models, key=os.path.getmtime)
        print(f"Found final model: {latest_final_model}")
        return latest_final_model, 'final'

    # 如果没有最终模型，查找检查点
    checkpoint_pattern = os.path.join(logs_dir, '**/checkpoints/checkpoint_*.zip')
    checkpoints = glob.glob(checkpoint_pattern, recursive=True)

    if not checkpoints:
        print("No saved models found")
        return None, None

    # 按修改时间排序，获取最新的检查点
    latest_checkpoint = max(checkpoints, key=os.path.getmtime)

    # 从文件名中提取步数
    import re
    match = re.search(r'checkpoint_(\d+)_steps', latest_checkpoint)
    if match:
        steps = match.group(1)
        print(f"Found latest checkpoint: {latest_checkpoint} (at {steps} steps)")
    else:
        print(f"Found latest checkpoint: {latest_checkpoint}")

    return latest_checkpoint, 'checkpoint'


def load_model_with_compatibility(algo, model_path, env):
    """使用兼容性机制加载模型"""
    print(f"Loading {algo} model from {model_path}")
    
    # 兼容性加载：先尝试不带环境检查，失败时使用自定义对象
    try:
        if algo == 'SAC':
            model = SAC.load(model_path, env=None)
        elif algo == 'TD3':
            model = TD3.load(model_path, env=None)
        elif algo == 'PPO':
            model = PPO.load(model_path, env=None)
        else:
            raise ValueError(f"Unsupported algorithm: {algo}")
        print("Model loaded successfully without space checking")
    except Exception as e:
        print(f"Failed to load model without env: {e}")
        print("Trying to load model with custom_objects to fix action_space check...")
        try:
            if algo == 'SAC':
                model = SAC.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            elif algo == 'TD3':
                model = TD3.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            elif algo == 'PPO':
                model = PPO.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            else:
                raise ValueError(f"Unsupported algorithm: {algo}")
            print("Model loaded successfully with custom_objects")
        except Exception as e2:
            print(f"Failed to load model with custom_objects: {e2}")
            raise e2
    
    # 手动设置环境，确保正确的环境检查
    model.set_env(env)
    return model


def continue_training_with_stability(model_path, model_type, config_file, additional_steps):
    """带稳定性机制的继续训练"""
    
    # 读取配置
    cfg = ConfigParser()
    
    # 尝试多个可能的配置文件位置
    possible_config_paths = [
        f'configs/{config_file}.ini',     # 从项目根目录运行
        f'../configs/{config_file}.ini',  # 从scripts目录运行
        os.path.join(os.path.dirname(__file__), '..', 'configs', f'{config_file}.ini')  # 相对于脚本位置
    ]
    
    config_path = None
    for path in possible_config_paths:
        if os.path.exists(path):
            config_path = path
            break
    
    if config_path is None:
        raise FileNotFoundError(f"Config file '{config_file}.ini' not found in any of these locations: {possible_config_paths}")
    
    print(f"Using config file: {os.path.abspath(config_path)}")
    cfg.read(config_path, encoding='utf-8')

    # 获取算法类型
    algo = cfg.get('options', 'algo')

    # 创建环境
    print("Setting up environment...")
    training_thread = TrainingThread(config_path)
    env = training_thread.env

    # 使用兼容性机制加载模型
    model = load_model_with_compatibility(algo, model_path, env)
    print(f"Model loaded successfully. Current timesteps: {model.num_timesteps}")

    # 确定保存路径 - 根据环境名称创建对应文件夹
    timestamp = datetime.now().strftime('%Y_%m_%d_%H_%M')

    # 从配置中获取环境名称
    env_name = cfg.get('options', 'env_name')
    dynamic_name = cfg.get('options', 'dynamic_name')
    algo = cfg.get('options', 'algo')
    policy_name = cfg.get('options', 'policy_name')

    # 确定logs目录位置
    if os.path.exists('logs'):
        logs_base = 'logs'
    elif os.path.exists('../logs'):
        logs_base = '../logs'
    else:
        logs_base = os.path.join(os.path.dirname(__file__), '..', 'logs')
        os.makedirs(logs_base, exist_ok=True)

    # 创建环境特定的目录结构
    env_logs_dir = os.path.join(logs_base, env_name)
    os.makedirs(env_logs_dir, exist_ok=True)

    # 生成描述性的文件夹名称
    folder_name = f'{timestamp}_{dynamic_name}_{policy_name}_{algo}_continued'
    if model_type == 'final':
        folder_name += '_from_final'
    else:
        folder_name += '_from_checkpoint'

    new_model_path = os.path.join(env_logs_dir, folder_name)

    print(f"Environment: {env_name}")
    print(f"Creating new training directory: {new_model_path}")
    if model_type == 'final':
        print("Continuing from final model")
    else:
        print("Continuing from checkpoint")

    model_save_path = os.path.join(new_model_path, 'models')
    checkpoint_save_path = os.path.join(new_model_path, 'models', 'checkpoints')
    config_save_path = os.path.join(new_model_path, 'config')

    # 创建所有必要的目录
    os.makedirs(model_save_path, exist_ok=True)
    os.makedirs(checkpoint_save_path, exist_ok=True)
    os.makedirs(config_save_path, exist_ok=True)

    # 保存配置文件到新目录（使用相同的格式化逻辑，去除中文注释）
    try:
        config_dest = os.path.join(config_save_path, 'config.ini')
        with open(config_dest, 'w', encoding='utf-8') as configfile:
            cfg.write(configfile)
        print(f"Configuration file saved to new directory (formatted, comments removed)")
    except Exception as e:
        print(f"Warning: Could not save config file: {e}")

    # 记录原始模型信息
    info_file = os.path.join(new_model_path, 'training_info.txt')
    with open(info_file, 'w') as f:
        f.write(f"Continue Training Information\n")
        f.write(f"=" * 40 + "\n")
        f.write(f"Original model: {model_path}\n")
        f.write(f"Model type: {model_type}\n")
        f.write(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Additional steps: {additional_steps}\n")
        f.write(f"Original timesteps: {model.num_timesteps}\n")
        f.write(f"Config file: {config_file}\n")
        f.write(f"Stability enhancements: Enabled\n")
    print(f"Training info saved to: {info_file}")

    print(f"Continuing training for {additional_steps} steps...")

    # 创建检查点回调
    from stable_baselines3.common.callbacks import BaseCallback

    class StableCheckpointCallback(BaseCallback):
        def __init__(self, save_freq=5000, checkpoint_path=None, model_path=None, verbose=1):
            super(StableCheckpointCallback, self).__init__(verbose)
            self.save_freq = save_freq
            self.checkpoint_path = checkpoint_path
            self.model_path = model_path

        def _on_step(self) -> bool:
            if self.n_calls % self.save_freq == 0:
                checkpoint_name = f"checkpoint_{self.model.num_timesteps}_steps"
                checkpoint_file = os.path.join(self.checkpoint_path, f"{checkpoint_name}.zip")
                self.model.save(checkpoint_file)
                if self.verbose > 0:
                    print(f"Step {self.model.num_timesteps}: Checkpoint saved to {checkpoint_file}")
            return True

        def _on_training_end(self):
            """训练结束时保存最终模型"""
            final_model_path = os.path.join(self.model_path, 'model_sb3.zip')
            self.model.save(final_model_path)
            if self.verbose > 0:
                print(f"Final model saved: {final_model_path}")

    # 创建检查点回调
    checkpoint_callback = StableCheckpointCallback(
        save_freq=5000,
        checkpoint_path=checkpoint_save_path,
        model_path=model_save_path,
        verbose=1
    )

    # 继续训练
    model.learn(
        total_timesteps=additional_steps,
        reset_num_timesteps=False,  # 重要：不重置时间步计数
        log_interval=1,
        callback=checkpoint_callback
    )

    print(f"Training completed!")
    print(f"Total timesteps: {model.num_timesteps}")

    return model


def main():
    parser = get_parser()
    args = parser.parse_args()
    
    print("🚀 Stable Continue Training Script")
    print("=" * 60)
    
    # 确定模型路径
    if args.model_path:
        model_path = args.model_path
        model_type = 'checkpoint' if 'checkpoint_' in model_path else 'final'
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            return
    else:
        print("🔍 Searching for latest model...")
        model_path, model_type = find_best_model()
        if model_path is None:
            print("❌ No model found to continue training")
            return

    config_file = args.config
    additional_steps = args.additional_steps

    print(f"📋 Training Configuration:")
    print(f"   Model path: {model_path}")
    print(f"   Model type: {model_type}")
    print(f"   Config file: {config_file}")
    print(f"   Additional steps: {additional_steps}")
    print(f"   Stability features: ✅ Enabled")
    print("=" * 60)

    try:
        model = continue_training_with_stability(model_path, model_type, config_file, additional_steps)
        print("🎉 Training continuation completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during training continuation: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
