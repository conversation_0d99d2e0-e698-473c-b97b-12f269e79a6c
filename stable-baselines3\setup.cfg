[metadata]
# This includes the license file in the wheel.
license_file = LICENSE

[tool:pytest]
# Deterministic ordering for tests; useful for pytest-xdist.
env =
	PYTHONHASHSEED=0
filterwarnings =
		# Tensorboard warnings
		ignore::DeprecationWarning:tensorboard
		# Gym warnings
		ignore:Parameters to load are deprecated.:DeprecationWarning
		ignore:the imp module is deprecated in favour of importlib:PendingDeprecationWarning
		ignore::UserWarning:gym

[pytype]
inputs = stable_baselines3
disable = pyi-error

[flake8]
ignore = W503,W504,E203,E231  # line breaks before and after binary operators
# Ignore import not used when aliases are defined
per-file-ignores =
	./stable_baselines3/__init__.py:F401
	./stable_baselines3/common/__init__.py:F401
	./stable_baselines3/common/envs/__init__.py:F401
	./stable_baselines3/a2c/__init__.py:F401
	./stable_baselines3/ddpg/__init__.py:F401
	./stable_baselines3/dqn/__init__.py:F401
	./stable_baselines3/her/__init__.py:F401
	./stable_baselines3/ppo/__init__.py:F401
	./stable_baselines3/sac/__init__.py:F401
	./stable_baselines3/td3/__init__.py:F401
	./stable_baselines3/common/vec_env/__init__.py:F401
exclude =
    # No need to traverse our git directory
    .git,
    # There's no value in checking cache directories
    __pycache__,
    # Don't check the doc
    docs/
    # This contains our built documentation
    build,
    # This contains builds of flake8 that we don't want to check
    dist
		*.egg-info
max-complexity = 15
# The GitHub editor is 127 chars wide
max-line-length = 127

[isort]
profile = black
line_length = 127
src_paths = stable_baselines3
