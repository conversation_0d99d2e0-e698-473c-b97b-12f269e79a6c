# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    # Skip CI if [ci skip] in the commit message
    if: "! contains(toJSON(github.event.commits.*.message), '[ci skip]')"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.7, 3.8, 3.9]

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        # cpu version of pytorch
        pip install torch==1.8.1+cpu -f https://download.pytorch.org/whl/torch_stable.html
        pip install .[extra,tests,docs]
        # Use headless version
        pip install opencv-python-headless
        # Tmp fix: ROM missing in the newest atari-py version
        pip install atari-py==0.2.5
    - name: Build the doc
      run: |
        make doc
    - name: Type check
      run: |
        make type
    - name: Check codestyle
      run: |
        make check-codestyle
    - name: Lint with flake8
      run: |
        make lint
    - name: Test with pytest
      run: |
        make pytest
