[options]
project_name = FlappingWing
env_name = Forest
dynamic_name = SimpleFixedwing

navigation_3d = False
using_velocity_state = False
reward_type = reward_final

;depth, lgmd, vector means scalar input
perception = vector

algo = SAC
total_timesteps = 100000

policy_name = mlp
net_arch = [32, 16]
activation_function = tanh
cnn_feature_num = 5

keyboard_debug = False
generate_q_map = False
q_map_save_steps = 10000

use_wandb = False
wandb_run_name = forest_sac_lgmd_5_new_reward
notes = no

state_feature_num = 1

[environment]
max_depth_meters = 20
screen_height = 60
screen_width = 90

crash_distance = 3
accept_radius = 3

[fixedwing]
dt = 0.1
v_xy_max = 15.0
v_xy_min = 5.0
v_z_max = 5.0
roll_max_deg = 45.0
roll_rate_max_deg = 100.0
pitch_max_deg = 20.0

pitch_flap_hz = 0
pitch_flap_deg = 3

; configs for DRL algorithms
[DRL]
gamma = 0.99
learning_rate = 1e-3
learning_starts = 1000
buffer_size = 50000
batch_size = 128
train_freq = 100
gradient_steps = 200
action_noise_sigma = 0.1
