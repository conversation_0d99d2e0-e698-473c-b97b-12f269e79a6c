# 无人机目标检测与撞击系统

本项目在原有的无人机导航避障系统基础上，新增了装甲车目标检测和直线撞击功能。系统采用模块化设计，支持与现有DRL导航系统的无缝集成。

## 功能特性

- **YOLO v5n目标检测**: 使用轻量级YOLO模型检测装甲车
- **3D坐标恢复**: 结合RGB和深度图像恢复目标3D位置
- **智能阶段切换**: 距离目标15米时自动启用检测模式
- **直线撞击**: 检测到目标后以5m/s速度直线撞击
- **模块化设计**: 与原导航系统解耦，支持独立运行
- **向后兼容**: 原有导航脚本无需修改即可继续使用
- **扩展架构**: 基于成熟的EvaluateThread框架构建

## 系统架构

```
目标检测系统 (v2.0)
├── 核心组件
│   ├── ArmoredVehicleDetector        # YOLO检测器 (scripts/utils/target_detection.py)
│   ├── CollisionController           # 撞击控制器
│   └── AirsimGymEnv (扩展)          # 环境集成
├── 线程架构
│   ├── EvaluateThread               # 原始评估线程 (保持不变)
│   ├── TargetDetectionEvaluateThread # 扩展评估线程 (新增)
│   └── NavigationWithDetectionThread # 综合测试线程 (重构)
└── 测试和训练工具
    ├── install_target_detection.py  # 自动安装脚本
    ├── test_target_detection.py     # 独立测试
    └── navigation_with_target_detection.py # 综合测试
```

## 快速开始

### 1. 环境准备
```bash
# 自动安装所有依赖
python install_target_detection.py

# 验证安装
python -c "from scripts.utils.target_detection import ArmoredVehicleDetector; print('安装成功!')"
```

### 2. 独立测试目标检测
```bash
# 测试YOLO检测功能（无需DRL模型）
python scripts/test_target_detection.py
```

### 3. 综合测试（导航+检测+撞击）
```bash
# 完整的导航避障+目标检测+撞击流程
python scripts/navigation_with_target_detection.py
```

### 4. 原始导航避障测试（不含目标检测）
```bash
# 原有功能保持不变，向后兼容
python scripts/start_evaluate_with_plot.py
```

### 5. 配置自定义模型（可选）
```bash
# 如果有自定义训练的YOLO模型
# 1. 将模型文件放在 models/ 目录下
# 2. 修改配置文件中的 model_path 参数
# 3. 重新运行测试脚本
```

## 详细使用说明

### 新架构说明 (v2.0)

项目采用了基于继承的扩展架构，确保代码复用和向后兼容：

```python
# 架构层次
EvaluateThread                    # 原始评估线程（保持不变）
    ↓ 继承
TargetDetectionEvaluateThread     # 扩展线程（新增目标检测功能）
    ↓ 继承
NavigationWithDetectionThread     # 应用线程（简化的综合测试）
```

### 主程序集成使用

**方式1: 使用原有脚本（无目标检测）**
```bash
# 原有导航避障功能，随机目标位置
python scripts/start_evaluate_with_plot.py
```

**方式2: 使用新的综合脚本（含目标检测）**
```bash
# 导航+检测+撞击，固定目标位置
python scripts/navigation_with_target_detection.py
```

### 独立测试模式

使用 `scripts/test_target_detection.py` 进行独立测试：

- 在装甲车附近起飞
- 自动扫描检测目标
- 检测到目标后自动撞击
- 无需DRL模型，专注测试检测功能

### 配置参数

在配置文件中添加目标检测相关参数：

```ini
# config/config.ini
[target_detection]
model_path = models/custom_armored_vehicle.pt
confidence_threshold = 0.5
collision_speed = 5.0
detection_distance = 15.0

# 如果不配置，系统会使用默认的预训练模型
```

## 工作流程

### 阶段化执行流程
1. **导航阶段**: 无人机使用DRL模型进行导航避障，飞向目标区域
2. **距离触发**: 当距离目标点15米时自动启用目标检测功能
3. **检测阶段**: 继续使用DRL导航，同时进行YOLO目标检测
4. **3D定位**: 将2D检测框结合深度信息转换为3D世界坐标
5. **模式切换**: 检测到装甲车后立即停止导航，切换到撞击控制
6. **撞击阶段**: 以5m/s速度直线飞向目标，完成撞击任务

### 双模式支持
- **标准模式**: 使用原有EvaluateThread，随机目标位置，无目标检测
- **检测模式**: 使用TargetDetectionEvaluateThread，固定目标位置，启用检测

## 核心组件

### ArmoredVehicleDetector (`scripts/utils/target_detection.py`)
- **YOLO v5n模型**: 轻量级目标检测，支持GPU加速
- **图像处理**: RGB图像预处理和后处理
- **坐标转换**: 2D检测框到3D世界坐标的精确转换
- **相机标定**: 支持AirSim相机参数配置（256x144, 90°FOV）
- **检测可视化**: 实时显示检测结果和置信度

### CollisionController (`scripts/utils/target_detection.py`)
- **方向计算**: 精确计算从当前位置到目标的撞击向量
- **偏航控制**: 自动调整无人机朝向，对准目标
- **速度控制**: 恒定5m/s撞击速度，确保撞击效果
- **完成检测**: 自动判断撞击是否完成

### 线程架构扩展
- **EvaluateThread**: 原始评估框架，成熟稳定，保持不变
- **TargetDetectionEvaluateThread**: 继承扩展，添加目标检测功能
- **NavigationWithDetectionThread**: 应用层封装，简化使用

### 环境集成 (`gym_env/gym_env/envs/airsim_env.py`)
- **状态管理**: collision_mode状态控制和阶段切换
- **图像获取**: RGB和深度图像的实时获取和处理
- **检测触发**: 基于距离的自动检测启用机制
- **模式切换**: DRL导航与撞击控制的无缝切换

## 自定义模型训练

详细的YOLO模型训练指南请参考：[YOLO_Training_Guide.md](YOLO_Training_Guide.md)

### 训练步骤概述：
1. 在AirSim中收集装甲车图像数据
2. 使用LabelImg等工具标注数据
3. 配置YOLO训练参数
4. 训练自定义检测模型
5. 部署到项目中使用

## 性能优化

### 检测性能
- 使用GPU加速推理
- 调整置信度阈值
- 优化图像分辨率

### 撞击精度
- 调整偏航角控制参数
- 优化速度控制策略
- 改善3D坐标转换精度

## 故障排除

### 常见问题

1. **YOLO模型加载失败**
   ```
   解决方案: 检查网络连接，首次使用需下载预训练模型
   ```

2. **检测不到目标**
   ```
   解决方案: 
   - 检查置信度阈值设置
   - 确认目标在相机视野内
   - 验证模型是否适合当前场景
   ```

3. **撞击方向不准确**
   ```
   解决方案:
   - 检查相机内参设置
   - 验证深度图像质量
   - 调整坐标转换参数
   ```

### 调试模式

启用详细日志输出：
```python
# 在环境初始化时
env.target_detector.debug = True
env.collision_controller.debug = True
```

## 扩展功能

### 多目标检测
- 支持检测多个装甲车
- 选择最近目标进行撞击
- 目标优先级排序

### 检测类别扩展
- 添加更多检测类别
- 不同目标的撞击策略
- 威胁等级评估

### 智能撞击策略
- 预测目标运动轨迹
- 最优撞击角度计算
- 多阶段撞击控制

## 技术支持

如有问题或建议，请：
1. 查看详细日志输出
2. 检查配置文件设置
3. 参考训练指南文档
4. 运行独立测试脚本验证功能

## 文件结构

```
项目根目录/
├── scripts/
│   ├── utils/
│   │   ├── target_detection.py              # 核心检测和撞击组件
│   │   ├── thread_evaluation.py             # 原始评估线程（保持不变）
│   │   ├── thread_target_detection_evaluation.py # 扩展评估线程
│   │   └── ui_train.py                      # GUI界面
│   ├── navigation_with_target_detection.py  # 综合测试脚本（重构版）
│   ├── test_target_detection.py            # 独立测试脚本
│   └── start_evaluate_with_plot.py         # 原始评估脚本（保持不变）
├── gym_env/gym_env/envs/
│   └── airsim_env.py                       # 环境集成（已扩展）
├── install_target_detection.py             # 自动安装脚本
├── YOLO_Training_Guide.md                  # YOLO训练指南（已更新）
├── Target_Detection_README.md             # 本文档（已更新）
└── models/                                 # 模型文件目录
    └── custom_armored_vehicle.pt           # 自定义模型（可选）
```

## 更新日志

### v2.0 (当前版本)
- ✅ **架构重构**: 基于继承的扩展架构，确保代码复用
- ✅ **向后兼容**: 原有脚本无需修改即可继续使用
- ✅ **模块化设计**: 清晰的组件分离和职责划分
- ✅ **自动安装**: 提供一键安装脚本
- ✅ **文档更新**: 完整的使用指南和训练教程

### v1.0 (初始版本)
- ✅ 基础目标检测和撞击功能
- ✅ 支持YOLO v5n模型
- ✅ 集成到现有导航系统
- ✅ 提供独立测试工具

## 技术支持与贡献

### 问题反馈
如遇到问题，请按以下步骤排查：
1. 检查依赖安装：`python install_target_detection.py`
2. 验证模型加载：运行独立测试脚本
3. 查看详细日志：启用debug模式
4. 检查配置文件：确认参数设置正确

### 开发贡献
欢迎贡献代码和改进建议：
- 新的检测模型集成
- 性能优化和bug修复
- 文档完善和示例添加
- 新功能开发和测试
