#!/usr/bin/env python3
"""
独立的目标检测测试脚本
用于测试YOLO检测模块，在装甲车附近起飞并进行检测撞击测试
"""

import sys
import os
import time
import cv2
import numpy as np
import math
from configparser import ConfigParser

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'gym_env'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))  # 添加utils路径

import gym_env
import gym
import airsim


class TargetDetectionTester:
    """目标检测测试器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化测试器
        
        Args:
            config_file: 配置文件路径
        """
        # 加载配置
        if config_file is None:
            config_file = os.path.join(os.path.dirname(__file__), '..', 'configs', 'config_My_tree_1_SimpleMultirotor.ini')
        
        self.cfg = ConfigParser()
        try:
            self.cfg.read(config_file, encoding='utf-8')
        except UnicodeDecodeError:
            self.cfg.read(config_file)
        
        # 修改配置以适应测试
        self.cfg.set('options', 'env_name', 'My_tree_1')
        self.cfg.set('options', 'dynamic_name', 'Multirotor')
        
        # 创建环境
        self.env = gym.make('airsim-env-v0')
        self.env.set_config(self.cfg)
        
        # 设置测试参数
        self.takeoff_height = 7.0
        self.detection_enabled = False
        self.collision_started = False
        
        print("Target detection tester initialized")
    
    def setup_test_environment(self):
        """设置测试环境"""
        # 启用目标检测
        self.env.enable_target_detection(True)

        print(f"Test environment setup complete")
        print(f"Takeoff height: {self.takeoff_height}m")
    
    def manual_takeoff_and_hover(self):
        """手动起飞并悬停"""
        print("Starting manual takeoff...")

        # 设置起始位置（在装甲车附近）
        # 假设装甲车在 (-200, -200) 附近，无人机在其附近起飞
        start_position = [-190, -190, 0]  # 在装甲车附近的地面
        self.env.dynamic_model.set_start(start_position, random_angle=0)
        print(f"Set spawn position: {start_position}")

        # 设置一个临时目标位置来触发检测逻辑（距离15米内）
        # 这个目标位置只是为了满足检测触发条件，实际撞击目标由检测结果决定
        temp_target = [-200, -200, 7]  # 装甲车大概位置
        self.env.dynamic_model._set_goal_pose_single(temp_target)
        print(f"Set temporary target for detection trigger: {temp_target}")
        print("注意：实际撞击目标将由YOLO检测结果动态确定")

        # 重置环境（这会应用新的起始位置）
        obs = self.env.reset()

        # 起飞到指定高度
        takeoff_steps = 0
        max_takeoff_steps = 100

        while takeoff_steps < max_takeoff_steps:
            current_pos = self.env.dynamic_model.get_position()

            if current_pos[2] >= self.takeoff_height - 0.5:
                print(f"Takeoff complete at height: {current_pos[2]:.2f}m")
                break

            # 纯垂直上升动作（无水平速度）
            if self.env.dynamic_model.navigation_3d:
                action = np.array([0.0, 1.0, 0.0])  # [v_xy=0, v_z=1.0, yaw_rate=0] - 纯垂直起飞
            else:
                action = np.array([0.0, 0.0])  # [v_xy=0, yaw_rate=0] - 2D模式下保持静止

            obs, reward, done, info = self.env.step(action)
            takeoff_steps += 1

            if done:
                print("Episode ended during takeoff")
                return False

        return True
    
    def test_detection_and_collision(self):
        """测试检测和撞击功能"""
        print("Starting detection and collision test...")
        
        if not self.manual_takeoff_and_hover():
            print("Takeoff failed")
            return
        
        # 悬停并开始检测
        hover_steps = 0
        max_hover_steps = 200
        
        print("Hovering and scanning for targets...")
        while hover_steps < max_hover_steps:
            # 悬停动作
            if self.env.dynamic_model.navigation_3d:
                action = np.array([0.0, 0.0, 0.4])  # [v_xy=0, v_z=0, yaw_rate=0.4弧度/秒≈23°/秒] 旋转扫描
            else:
                action = np.array([0.0, 0.4])  # [v_xy=0, yaw_rate=0.4弧度/秒≈23°/秒] 旋转扫描
                
            obs, reward, done, info = self.env.step(action)
            hover_steps += 1
            
            # 检查是否检测到目标
            if hasattr(self.env, 'last_detections') and self.env.last_detections:
                print(f"Detected {len(self.env.last_detections)} targets!")
                for i, detection in enumerate(self.env.last_detections):
                    print(f"Target {i+1}: confidence={detection['confidence']:.2f}, "
                          f"position_3d={detection.get('position_3d', 'None')}")
                break
                
            if done:
                print("Episode ended during scanning")
                return
        
        # 如果检测到目标，等待撞击模式启动
        if hasattr(self.env, 'collision_mode') and self.env.collision_mode:
            print("Collision mode activated! Starting collision sequence...")
            
            collision_steps = 0
            max_collision_steps = 200
            
            while collision_steps < max_collision_steps:
                # 在撞击模式下，动作会被撞击控制器覆盖
                action = np.array([0.0, 0.0, 0.0])  # 占位动作
                
                obs, reward, done, info = self.env.step(action)
                collision_steps += 1
                
                current_pos = self.env.dynamic_model.get_position()
                print(f"Collision step {collision_steps}: position={current_pos}")
                
                if done:
                    if info.get('is_crash'):
                        print("Collision successful! Target hit!")
                    else:
                        print(f"Episode ended: {info}")
                    break
                    
                # 检查是否完成撞击
                if hasattr(self.env.collision_controller, 'collision_active'):
                    if not self.env.collision_controller.collision_active:
                        print("Collision sequence completed")
                        break
        else:
            print("No targets detected or collision mode not activated")
    
    def run_test(self):
        """运行完整测试"""
        print("="*50)
        print("Starting Target Detection Test")
        print("="*50)
        
        try:
            self.setup_test_environment()
            self.test_detection_and_collision()
            
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
        except Exception as e:
            print(f"Test failed with error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print("Test completed")


def main():
    """主函数"""
    # 可以指定配置文件路径
    config_file = None
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    # 创建并运行测试器
    tester = TargetDetectionTester(config_file)
    tester.run_test()


if __name__ == "__main__":
    main()
