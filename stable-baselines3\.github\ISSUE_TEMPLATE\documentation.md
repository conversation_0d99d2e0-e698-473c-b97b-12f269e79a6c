---
name: "\U0001F4DA Documentation"
about: Report an issue related to Stable-Baselines3 documentation
labels: documentation
---

**Important Note: We do not do technical support, nor consulting** and don't answer personal questions per email.
Please post your question on the [RL Discord](https://discord.com/invite/xhfNqQv), [Reddit](https://www.reddit.com/r/reinforcementlearning/) or [Stack Overflow](https://stackoverflow.com/) in that case.

### 📚 Documentation

A clear and concise description of what should be improved in the documentation.

### Checklist

- [ ] I have read the [documentation](https://stable-baselines3.readthedocs.io/en/master/) (**required**)
- [ ] I have checked that there is no similar [issue](https://github.com/DLR-RM/stable-baselines3/issues) in the repo (**required**)



<!--- This Template is an edited version of the one from https://github.com/pytorch/pytorch -->
