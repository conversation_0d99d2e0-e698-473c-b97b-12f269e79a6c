#!/usr/bin/env python3
"""
导航避障+目标检测撞击综合测试脚本
无人机先进行导航避障到接近目标点，然后开启检测，检测到装甲车后进行撞击
"""

import sys
import os
from PyQt5 import QtWidgets

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'gym_env'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))  # 添加utils路径

from utils.ui_train import TrainingUi
from utils.thread_target_detection_evaluation import TargetDetectionEvaluateThread


class NavigationWithDetectionThread(TargetDetectionEvaluateThread):
    """导航+检测综合测试线程"""

    def __init__(self, config_file, model_file, target_position=None):
        # 设置默认目标位置
        target_pos = target_position or [-200, -200, 7]

        # 调用父类初始化，使用固定目标位置和目标检测功能
        super().__init__(
            eval_path="",  # 不需要评估路径
            config=config_file,
            model_file=model_file,
            eval_ep_num=1,  # 单次任务
            target_position=target_pos,
            detection_distance_threshold=15.0
        )

        print(f"Navigation with detection initialized using TargetDetectionEvaluateThread")
        print(f"Target position: {target_pos}")
        print(f"Detection threshold: 15.0m")
    
    # _load_model方法现在由父类TargetDetectionEvaluateThread提供
    
    # run方法现在由父类TargetDetectionEvaluateThread提供
    # 父类会自动处理导航→检测→撞击的完整流程
    pass


def main():
    """主函数"""
    # 设置路径
    eval_path = r'logs\My_tree_1\2025_06_30_10_12_Multirotor_No_CNN_SAC'
    config_file = eval_path + '/config/config.ini'
    model_file = eval_path + '/models/model_sb3.zip'
    
    # 设置目标位置（可以根据需要修改）
    target_position = [-200, -200, 7]  # [x, y, z]
    
    print("Navigation + Target Detection Test")
    print(f"Model: {model_file}")
    print(f"Target: {target_position}")
    
    # 创建Qt应用
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建GUI（可选，用于可视化）
    gui = TrainingUi(config_file)
    gui.show()

    # 创建并启动测试线程
    test_thread = NavigationWithDetectionThread(config_file, model_file, target_position)

    # 连接信号（如果需要可视化）
    test_thread.env.action_signal.connect(gui.action_cb)
    test_thread.env.state_signal.connect(gui.state_cb)
    test_thread.env.attitude_signal.connect(gui.attitude_plot_cb)
    test_thread.env.reward_signal.connect(gui.reward_plot_cb)
    test_thread.env.pose_signal.connect(gui.traj_plot_cb)
    
    test_thread.start()
    
    # 运行Qt事件循环
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
