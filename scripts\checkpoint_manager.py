"""
检查点管理器 - 管理训练检查点的工具
"""

import os
import glob
import re
from datetime import datetime
from typing import List, Tuple, Optional


class CheckpointManager:
    """训练检查点管理器"""

    def __init__(self, logs_dir: str = '../logs'):
        self.logs_dir = logs_dir
    
    def find_all_checkpoints(self) -> List[Tuple[str, int, datetime]]:
        """
        查找所有检查点文件
        返回: [(文件路径, 步数, 修改时间), ...]
        """
        if not os.path.exists(self.logs_dir):
            return []

        # 查找所有检查点文件
        checkpoint_pattern = os.path.join(self.logs_dir, '**/checkpoints/checkpoint_*.zip')
        checkpoint_files = glob.glob(checkpoint_pattern, recursive=True)
        
        checkpoints = []
        for file_path in checkpoint_files:
            # 从文件名中提取步数
            match = re.search(r'checkpoint_(\d+)_steps', file_path)
            if match:
                steps = int(match.group(1))
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                checkpoints.append((file_path, steps, mod_time))
        
        # 按步数排序
        checkpoints.sort(key=lambda x: x[1])
        return checkpoints
    
    def find_latest_checkpoint(self) -> Optional[str]:
        """查找最新的检查点"""
        checkpoints = self.find_all_checkpoints()
        if not checkpoints:
            return None
        
        # 返回最新的（按修改时间）
        latest = max(checkpoints, key=lambda x: x[2])
        return latest[0]
    
    def find_checkpoint_by_steps(self, target_steps: int) -> Optional[str]:
        """根据步数查找检查点"""
        checkpoints = self.find_all_checkpoints()
        
        # 查找最接近目标步数的检查点
        best_match = None
        min_diff = float('inf')
        
        for file_path, steps, _ in checkpoints:
            diff = abs(steps - target_steps)
            if diff < min_diff:
                min_diff = diff
                best_match = file_path
        
        return best_match
    
    def list_checkpoints(self):
        """列出所有检查点"""
        checkpoints = self.find_all_checkpoints()
        
        if not checkpoints:
            print("No checkpoints found.")
            return
        
        print("Available checkpoints:")
        print("-" * 80)
        print(f"{'Steps':<10} {'Date':<20} {'Path'}")
        print("-" * 80)
        
        for file_path, steps, mod_time in checkpoints:
            date_str = mod_time.strftime('%Y-%m-%d %H:%M:%S')
            # 显示相对路径
            rel_path = os.path.relpath(file_path, self.logs_dir)
            print(f"{steps:<10} {date_str:<20} {rel_path}")
    
    def find_all_final_models(self) -> List[Tuple[str, datetime]]:
        """查找所有最终模型"""
        if not os.path.exists(self.logs_dir):
            return []

        model_pattern = os.path.join(self.logs_dir, '**/models/model_sb3.zip')
        model_files = glob.glob(model_pattern, recursive=True)
        
        models = []
        for file_path in model_files:
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            models.append((file_path, mod_time))
        
        # 按修改时间排序
        models.sort(key=lambda x: x[1])
        return models
    
    def list_all_models(self):
        """列出所有模型（包括检查点和最终模型）"""
        print("=" * 80)
        print("ALL SAVED MODELS")
        print("=" * 80)
        
        # 列出最终模型
        final_models = self.find_all_final_models()
        if final_models:
            print("\nFinal Models:")
            print("-" * 50)
            for file_path, mod_time in final_models:
                date_str = mod_time.strftime('%Y-%m-%d %H:%M:%S')
                rel_path = os.path.relpath(file_path, self.logs_dir)
                print(f"{date_str} - {rel_path}")
        
        # 列出检查点
        print("\nCheckpoints:")
        print("-" * 50)
        self.list_checkpoints()
    
    def clean_old_checkpoints(self, keep_latest: int = 5):
        """清理旧的检查点，只保留最新的几个"""
        checkpoints = self.find_all_checkpoints()
        
        if len(checkpoints) <= keep_latest:
            print(f"Only {len(checkpoints)} checkpoints found, no cleanup needed.")
            return
        
        # 按修改时间排序，删除旧的
        checkpoints.sort(key=lambda x: x[2])
        to_delete = checkpoints[:-keep_latest]
        
        print(f"Cleaning up {len(to_delete)} old checkpoints...")
        for file_path, steps, mod_time in to_delete:
            try:
                os.remove(file_path)
                print(f"Deleted: checkpoint at {steps} steps")
            except Exception as e:
                print(f"Failed to delete {file_path}: {e}")
        
        print(f"Cleanup completed. Kept {keep_latest} latest checkpoints.")


def main():
    """主函数 - 命令行界面"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Checkpoint Manager")
    parser.add_argument('--list', action='store_true', help='List all checkpoints')
    parser.add_argument('--list-all', action='store_true', help='List all models and checkpoints')
    parser.add_argument('--latest', action='store_true', help='Show latest checkpoint')
    parser.add_argument('--steps', type=int, help='Find checkpoint by steps')
    parser.add_argument('--clean', type=int, default=5, help='Clean old checkpoints, keep N latest')
    
    args = parser.parse_args()
    
    manager = CheckpointManager()
    
    if args.list:
        manager.list_checkpoints()
    elif args.list_all:
        manager.list_all_models()
    elif args.latest:
        latest = manager.find_latest_checkpoint()
        if latest:
            print(f"Latest checkpoint: {latest}")
        else:
            print("No checkpoints found")
    elif args.steps:
        checkpoint = manager.find_checkpoint_by_steps(args.steps)
        if checkpoint:
            print(f"Checkpoint near {args.steps} steps: {checkpoint}")
        else:
            print(f"No checkpoint found near {args.steps} steps")
    elif args.clean:
        manager.clean_old_checkpoints(args.clean)
    else:
        # 默认显示所有模型
        manager.list_all_models()


if __name__ == "__main__":
    main()
