[options]
env_name = My_tree_1
dynamic_name = Multirotor

navigation_3d = False
using_velocity_state = False
reward_type = reward_final

;depth, lgmd, vector
perception = depth

algo = SAC
total_timesteps = 100000

policy_name = No_CNN
net_arch = [64, 32, 16]
activation_function = tanh
cnn_feature_num = 25

keyboard_debug = False
generate_q_map = True
q_map_save_steps = 10000

use_wandb = False

[wandb]
name = My_tree_1-SM-SAC-2D
notes = Custom tree environment with 450x450m workspace

[environment]
max_depth_meters = 15
screen_height = 80
screen_width = 100

crash_distance = 2
accept_radius = 2

[multirotor]
dt = 0.1
acc_xy_max = 2.0
v_xy_max = 5
v_xy_min = 0.5
v_z_max = 2.0 
yaw_rate_max_deg = 30.0

; configs for DRL algorithms
[DRL]
gamma = 0.99
learning_rate = 1e-3
learning_starts = 2000
buffer_size = 50000
batch_size = 1280
train_freq = 200
gradient_steps = 200
action_noise_sigma = 0.1

; 稳定性相关配置
[stability]
; 每隔多少步检查一次连接健康状态
health_check_interval = 1000
; 最大重连尝试次数
max_reconnect_attempts = 3
; 图像请求最大重试次数
max_image_retries = 5
; 是否启用自动保存检查点
enable_checkpoint_save = True
; 检查点保存间隔（步数）
checkpoint_save_interval = 10000

; 检查点管理配置
[checkpoint]
; 检查点保存频率（步数）
save_freq = 5000
; 保留的检查点数量（自动清理旧的）
keep_checkpoints = 10
; 检查点文件名前缀
name_prefix = checkpoint
; 是否在控制台显示保存信息
verbose = True