#!/usr/bin/env python3
"""
目标检测和撞击控制模块
包含YOLO v5n装甲车检测和3D坐标恢复功能

修复内容:
- 深度单位处理: AirSim深度图像单位为厘米，转换为米进行计算
- 图像尺寸匹配: RGB(256x144) 与 Depth(100x80) 坐标映射
- 坐标系转换: 相机坐标系(右-下-前) -> 无人机坐标系(前-右-下) -> 世界坐标系

确认的系统设置:
- 相机安装: 无人机中心，朝向前方，ID="0"，FOV=90°
- 坐标系: 无人机位置为世界坐标，装甲车坐标转换到世界坐标系
- 用途: 确保坐标系一致性，支持准确的撞击向量计算和路径规划
"""

import numpy as np
import math
import os
from typing import List, Dict, Tuple

try:
    import yolov5
    YOLO_AVAILABLE = True
except ImportError:
    print("Warning: YOLOv5 not available. Please install with: pip install yolov5")
    YOLO_AVAILABLE = False


class ArmoredVehicleDetector:
    """装甲车检测器 - 使用YOLO v5n进行目标检测并恢复3D坐标"""
    
    def __init__(self, model_path: str = None, confidence_threshold: float = 0.5):
        """
        初始化检测器
        
        Args:
            model_path: YOLO模型路径，None则使用预训练模型
            confidence_threshold: 检测置信度阈值
        """
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.camera_width = 256
        self.camera_height = 144
        self.fov_degrees = 90
        self.debug = False
        
        # 相机内参（根据FOV计算）
        self._calculate_camera_intrinsics()
        
        # 初始化YOLO模型
        self._load_model(model_path)
        
    def _load_model(self, model_path: str = None):
        """加载YOLO模型"""
        if not YOLO_AVAILABLE:
            print("YOLOv5 not available, detection will be disabled")
            return
            
        try:
            if model_path and os.path.exists(model_path):
                # 加载自定义模型
                self.model = yolov5.load(model_path)
                print(f"Loaded custom YOLO model: {model_path}")
            else:
                # 加载预训练模型
                self.model = yolov5.load('yolov5n.pt')
                print("Loaded pre-trained YOLOv5n model")
                
            self.model.conf = self.confidence_threshold
            
        except Exception as e:
            print(f"Failed to load YOLO model: {e}")
            self.model = None
    
    def _calculate_camera_intrinsics(self):
        """根据FOV计算相机内参"""
        # 计算焦距
        fov_rad = math.radians(self.fov_degrees)
        self.fx = self.camera_width / (2 * math.tan(fov_rad / 2))
        self.fy = self.camera_height / (2 * math.tan(fov_rad / 2))
        
        # 主点（图像中心）
        self.cx = self.camera_width / 2
        self.cy = self.camera_height / 2
        
        if self.debug:
            print(f"Camera intrinsics: fx={self.fx:.2f}, fy={self.fy:.2f}, cx={self.cx:.2f}, cy={self.cy:.2f}")
    
    def set_camera_parameters(self, width: int, height: int, fov_degrees: float):
        """设置相机参数"""
        self.camera_width = width
        self.camera_height = height
        self.fov_degrees = fov_degrees
        self._calculate_camera_intrinsics()
        
        if self.debug:
            print(f"Camera parameters updated: {width}x{height}, FOV={fov_degrees}°")
            print(f"Camera intrinsics: fx={self.fx:.2f}, fy={self.fy:.2f}, cx={self.cx:.2f}, cy={self.cy:.2f}")
    
    def detect_objects(self, rgb_image: np.ndarray) -> List[Dict]:
        """
        使用YOLO检测图像中的目标
        
        Args:
            rgb_image: RGB图像 (H, W, 3)
            
        Returns:
            检测结果列表，每个元素包含 {'bbox': [x1, y1, x2, y2], 'confidence': float, 'class': str}
        """
        if self.model is None:
            return []
            
        try:
            # YOLO推理
            results = self.model(rgb_image)
            detections = []
            
            # 解析结果
            for *box, conf, cls in results.xyxy[0].cpu().numpy():
                if conf >= self.confidence_threshold:
                    x1, y1, x2, y2 = map(int, box)
                    class_name = self.model.names[int(cls)]
                    
                    # 过滤装甲车相关类别（可根据实际情况调整）
                    if self._is_armored_vehicle(class_name):
                        detections.append({
                            'bbox': [x1, y1, x2, y2],
                            'confidence': float(conf),
                            'class': class_name,
                            'center': [(x1 + x2) // 2, (y1 + y2) // 2]
                        })
                        
                        if self.debug:
                            print(f"Detected {class_name} with confidence {conf:.3f} at bbox [{x1}, {y1}, {x2}, {y2}]")
            
            return detections
            
        except Exception as e:
            print(f"Detection failed: {e}")
            return []
    
    def _is_armored_vehicle(self, class_name: str) -> bool:
        """判断检测到的类别是否为装甲车"""
        # 装甲车相关的类别名称（根据实际模型调整）
        armored_vehicle_classes = [
            'tank', 'armored_vehicle', 'military_vehicle', 
            'car', 'truck', 'vehicle'  # 通用车辆类别，可根据需要调整
        ]
        return class_name.lower() in armored_vehicle_classes
    
    def pixel_to_3d(self, pixel_x: int, pixel_y: int, depth: float, 
                   drone_position: np.ndarray, drone_orientation: np.ndarray) -> np.ndarray:
        """
        将像素坐标转换为3D世界坐标
        
        Args:
            pixel_x, pixel_y: 像素坐标
            depth: 深度值（米）
            drone_position: 无人机位置 [x, y, z]
            drone_orientation: 无人机姿态 [roll, pitch, yaw] (弧度)
            
        Returns:
            3D世界坐标 [x, y, z]
        """
        if depth <= 0 or depth > 10000:  # 过滤无效深度（单位：厘米，100米=10000厘米）
            return None

        # AirSim深度图像经过处理后单位为厘米，需要转换为米
        # 原始: depth_img (米) -> 处理: depth_meter = depth_img * 100 (厘米)
        depth_meters = depth / 100.0

        # 1. 像素坐标转相机坐标系
        x_cam = (pixel_x - self.cx) * depth_meters / self.fx
        y_cam = (pixel_y - self.cy) * depth_meters / self.fy
        z_cam = depth_meters
        
        # 2. 相机坐标系转无人机坐标系
        # 相机坐标系：右-下-前 (x_cam, y_cam, z_cam)
        # 无人机坐标系：前-右-下 (x_drone, y_drone, z_drone)
        #
        # 相机安装假设：
        # - 相机安装在无人机中心（无平移偏移）
        # - 相机朝向无人机前方（AirSim默认设置）
        # - 相机ID="0"为默认前置相机
        #
        # 坐标系转换：
        # x_drone = z_cam (相机前方 = 无人机前方)
        # y_drone = x_cam (相机右方 = 无人机右方)
        # z_drone = y_cam (相机下方 = 无人机下方)
        point_drone = np.array([z_cam, x_cam, y_cam])
        
        # 3. 无人机坐标系转世界坐标系
        #
        # 无人机位置和姿态都在世界坐标系中：
        # - drone_position: 通过 simGetVehiclePose().position 获取的世界坐标
        # - drone_orientation: [roll, pitch, yaw] 世界坐标系下的姿态角
        #
        # 目标：将装甲车坐标也转换到世界坐标系，以便：
        # 1. 与无人机世界坐标进行比较和计算
        # 2. 计算准确的撞击向量和路径规划
        # 3. 确保坐标系一致性
        roll, pitch, yaw = drone_orientation

        # 构建旋转矩阵（ZYX顺序）
        R_z = np.array([[np.cos(yaw), -np.sin(yaw), 0],
                       [np.sin(yaw), np.cos(yaw), 0],
                       [0, 0, 1]])

        R_y = np.array([[np.cos(pitch), 0, np.sin(pitch)],
                       [0, 1, 0],
                       [-np.sin(pitch), 0, np.cos(pitch)]])

        R_x = np.array([[1, 0, 0],
                       [0, np.cos(roll), -np.sin(roll)],
                       [0, np.sin(roll), np.cos(roll)]])

        R = R_z @ R_y @ R_x

        # 转换到世界坐标系
        point_world = drone_position + R @ point_drone
        
        if self.debug:
            print(f"Pixel ({pixel_x}, {pixel_y}) with depth {depth:.2f}m -> World {point_world}")
        
        return point_world
    
    def detect_and_get_3d_positions(self, rgb_image: np.ndarray, depth_image: np.ndarray,
                                   drone_position: np.ndarray, drone_orientation: np.ndarray) -> List[Dict]:
        """
        检测目标并恢复3D位置

        Args:
            rgb_image: RGB图像 (H=144, W=256, C=3)
            depth_image: 深度图像 (H=80, W=100) - 注意尺寸不同
            drone_position: 无人机位置
            drone_orientation: 无人机姿态

        Returns:
            检测结果列表，包含3D位置信息
        """
        # 1. 进行目标检测
        detections = self.detect_objects(rgb_image)

        # 2. 为每个检测结果计算3D位置
        results = []
        for detection in detections:
            center_x, center_y = detection['center']

            # 将RGB图像坐标转换为深度图像坐标
            # RGB shape: (height=144, width=256, channels=3)
            # Depth shape: (height=80, width=100)
            # 注意：shape格式为(height, width)，即(y, x)
            rgb_height, rgb_width = rgb_image.shape[:2]  # (144, 256)
            depth_height, depth_width = depth_image.shape  # (80, 100)

            depth_x = int(center_x * depth_width / rgb_width)   # 256->100
            depth_y = int(center_y * depth_height / rgb_height) # 144->80

            if self.debug:
                print(f"RGB center: ({center_x}, {center_y}), Depth coord: ({depth_x}, {depth_y})")
                print(f"RGB size: {rgb_image.shape}, Depth size: {depth_image.shape}")
                print(f"Size mapping: RGB({rgb_height}x{rgb_width}) -> Depth({depth_height}x{depth_width})")

            # 确保坐标在深度图像范围内
            if 0 <= depth_x < depth_width and 0 <= depth_y < depth_height:
                # 获取中心点深度
                depth = depth_image[depth_y, depth_x]
                
                # 转换为3D坐标（使用原始RGB图像坐标）
                position_3d = self.pixel_to_3d(center_x, center_y, depth,
                                             drone_position, drone_orientation)
                
                result = detection.copy()
                result['position_3d'] = position_3d
                result['depth'] = float(depth)
                results.append(result)
                
                if self.debug and position_3d is not None:
                    print(f"Target at 3D position: {position_3d}")
            
        return results


class CollisionController:
    """撞击控制器 - 控制无人机直线撞击目标"""
    
    def __init__(self, collision_speed: float = 5.0):
        """
        初始化撞击控制器
        
        Args:
            collision_speed: 撞击速度 (m/s)
        """
        self.collision_speed = collision_speed
        self.target_position = None
        self.collision_started = False
        self.debug = False
        
    def set_target(self, target_position: np.ndarray):
        """设置撞击目标位置"""
        self.target_position = np.array(target_position)
        self.collision_started = True
        
        if self.debug:
            print(f"Collision target set: {self.target_position}")
    
    def calculate_collision_action(self, current_position: np.ndarray, 
                                 current_yaw: float) -> Tuple[np.ndarray, bool]:
        """
        计算撞击动作
        
        Args:
            current_position: 当前位置
            current_yaw: 当前偏航角
            
        Returns:
            (action, collision_complete): 动作向量和是否完成撞击
        """
        if self.target_position is None or not self.collision_started:
            return np.array([0, 0, 0, 0]), False
        
        # 计算到目标的向量
        direction_vector = self.target_position - current_position
        distance = np.linalg.norm(direction_vector)
        
        # 检查是否已经到达目标
        if distance < 2.0:  # 2米内认为撞击完成
            if self.debug:
                print("Collision completed!")
            return np.array([0, 0, 0, 0]), True
        
        # 归一化方向向量
        if distance > 0:
            direction_normalized = direction_vector / distance
        else:
            return np.array([0, 0, 0, 0]), True
        
        # 计算目标偏航角
        target_yaw = math.atan2(direction_normalized[1], direction_normalized[0])
        yaw_error = target_yaw - current_yaw
        
        # 角度归一化到 [-π, π]
        while yaw_error > math.pi:
            yaw_error -= 2 * math.pi
        while yaw_error < -math.pi:
            yaw_error += 2 * math.pi
        
        # 构建动作向量 [vx, vy, vz, yaw_rate]
        # 直线撞击：朝目标方向以固定速度飞行
        vx = direction_normalized[0] * self.collision_speed
        vy = direction_normalized[1] * self.collision_speed
        vz = direction_normalized[2] * self.collision_speed
        yaw_rate = yaw_error * 2.0  # 偏航角控制增益
        
        action = np.array([vx, vy, vz, yaw_rate])
        
        if self.debug:
            print(f"Collision action: distance={distance:.2f}m, action={action}")
        
        return action, False
    
    def stop_collision(self):
        """停止撞击"""
        self.collision_started = False
        self.target_position = None
        
        if self.debug:
            print("Collision stopped")
