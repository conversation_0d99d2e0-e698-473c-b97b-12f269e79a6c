"""
扩展EvaluateThread以支持目标检测和固定目标位置
保持与原有EvaluateThread的兼容性
"""

import numpy as np
from thread_evaluation import EvaluateThread


class TargetDetectionEvaluateThread(EvaluateThread):
    """
    扩展EvaluateThread类，添加目标检测和固定目标位置支持
    
    主要扩展功能：
    1. 支持固定目标位置（而不是随机目标）
    2. 启用目标检测功能
    3. 支持阶段切换逻辑（导航→检测→撞击）
    4. 保持与原有EvaluateThread的完全兼容性
    """
    
    def __init__(self, eval_path, config, model_file, eval_ep_num=1, 
                 target_position=None, detection_distance_threshold=15.0,
                 eval_env=None, eval_dynamics=None):
        """
        初始化目标检测评估线程
        
        Args:
            eval_path: 评估路径
            config: 配置文件路径
            model_file: 模型文件路径
            eval_ep_num: 评估episode数量，默认1（单次任务）
            target_position: 固定目标位置 [x, y, z]，如果为None则使用随机目标
            detection_distance_threshold: 检测距离阈值，默认15米
            eval_env: 环境名称
            eval_dynamics: 动力学模型名称
        """
        # 调用父类初始化
        super().__init__(eval_path, config, model_file, eval_ep_num, eval_env, eval_dynamics)
        
        # 目标检测相关配置
        self.target_position = target_position
        self.detection_distance_threshold = detection_distance_threshold
        self.use_fixed_target = target_position is not None
        
        # 如果指定了固定目标位置，配置环境
        if self.use_fixed_target:
            self._setup_target_detection()
            print(f"Target detection evaluation initialized")
            print(f"Fixed target position: {self.target_position}")
            print(f"Detection threshold: {self.detection_distance_threshold}m")
        else:
            print("Standard evaluation mode (random targets)")
    
    def _setup_target_detection(self):
        """配置目标检测功能"""
        # 设置固定目标位置
        self.env.dynamic_model.goal_position = np.array(self.target_position)
        
        # 启用目标检测
        self.env.enable_target_detection(True)
        self.env.detection_distance_threshold = self.detection_distance_threshold
        
        print("Target detection enabled")
    
    def run_drl_model(self):
        """
        重写DRL模型运行逻辑，添加阶段切换支持
        
        如果使用固定目标，则添加阶段切换逻辑：
        - 导航阶段：使用DRL模型导航到目标区域
        - 检测阶段：继续使用DRL导航，同时进行目标检测
        - 撞击阶段：由撞击控制器接管
        
        如果不使用固定目标，则完全复用父类逻辑
        """
        if not self.use_fixed_target:
            # 如果不使用固定目标，完全复用父类逻辑
            print("Using standard evaluation logic (random targets)")
            return super().run_drl_model()
        
        # 使用固定目标的增强逻辑
        print("Using target detection evaluation logic (fixed target)")
        return self._run_target_detection_evaluation()
    
    def _run_target_detection_evaluation(self):
        """运行目标检测评估的核心逻辑"""
        print('Start target detection evaluation')
        
        # 加载模型（复用父类逻辑）
        model = self._load_model_with_compatibility()
        
        # 手动设置环境，确保正确的环境检查
        model.set_env(self.env)
        self.env.model = model
        
        obs = self.env.reset()
        episode_num = 0
        time_step = 0
        reward_sum = np.array([.0])
        episode_successes = []
        episode_crashes = []
        
        # 阶段控制变量
        navigation_phase = True
        detection_phase = False
        collision_phase = False
        
        print("Phase 1: Navigation to target area...")
        
        while episode_num < self.eval_ep_num:
            step_count = 0
            max_steps = 3000
            done = False
            
            while not done and step_count < max_steps:
                step_count += 1
                
                # 获取当前状态
                distance_to_goal = self.env.get_distance_to_goal_3d()
                
                # 阶段判断和切换
                if navigation_phase and distance_to_goal <= self.detection_distance_threshold:
                    print(f"\nPhase 2: Target detection activated at distance {distance_to_goal:.2f}m")
                    navigation_phase = False
                    detection_phase = True
                
                if detection_phase and hasattr(self.env, 'collision_mode') and self.env.collision_mode:
                    print(f"\nPhase 3: Collision mode activated!")
                    detection_phase = False
                    collision_phase = True
                
                # 根据阶段选择动作（复用父类的模型预测逻辑）
                if navigation_phase or detection_phase:
                    # 导航阶段和检测阶段都使用DRL模型
                    unscaled_action, _ = model.predict(obs, deterministic=True)
                    phase_info = "Navigation" if navigation_phase else "Detection - Using DRL navigation while scanning"
                elif collision_phase:
                    # 撞击阶段：动作由撞击控制器处理
                    unscaled_action = np.array([0.0, 0.0, 0.0])  # 占位动作，会被撞击控制器覆盖
                    phase_info = "Collision - Attacking target"
                
                # 执行动作
                new_obs, reward, done, info = self.env.step(unscaled_action)
                
                # 输出状态信息
                if step_count % 50 == 0:
                    current_position = self.env.dynamic_model.get_position()
                    print(f"Step {step_count}: {phase_info}")
                    print(f"  Position: [{current_position[0]:.1f}, {current_position[1]:.1f}, {current_position[2]:.1f}]")
                    print(f"  Distance to goal: {distance_to_goal:.2f}m")
                
                obs = new_obs
                reward_sum[-1] += reward
                time_step += 1
            
            # Episode结束处理
            episode_num += 1
            maybe_is_success = info.get('is_success')
            maybe_is_crash = info.get('is_crash')
            
            print(f'Episode {episode_num}: reward={reward_sum[-1]:.2f}, success={maybe_is_success}, steps={step_count}')
            
            episode_successes.append(float(maybe_is_success))
            episode_crashes.append(float(maybe_is_crash))
            reward_sum = np.append(reward_sum, .0)
            
            if episode_num < self.eval_ep_num:
                obs = self.env.reset()
                # 重置阶段状态
                navigation_phase = True
                detection_phase = False
                collision_phase = False
        
        # 输出最终结果
        print(f'\nTarget Detection Evaluation Complete:')
        print(f'Average reward: {reward_sum[:self.eval_ep_num].mean():.2f}')
        print(f'Success rate: {np.mean(episode_successes):.2%}')
        print(f'Crash rate: {np.mean(episode_crashes):.2%}')
        
        # 存储结果到实例变量
        self.results = {
            'rewards': reward_sum[:self.eval_ep_num],
            'successes': episode_successes,
            'crashes': episode_crashes,
            'avg_reward': reward_sum[:self.eval_ep_num].mean(),
            'success_rate': np.mean(episode_successes),
            'crash_rate': np.mean(episode_crashes)
        }
    
    def _load_model_with_compatibility(self):
        """复用父类的模型加载逻辑"""
        from stable_baselines3 import TD3, SAC, PPO
        
        algo = self.cfg.get('options', 'algo')
        
        # 兼容性加载：先尝试不带环境检查，失败时使用自定义对象
        try:
            if algo == 'TD3':
                model = TD3.load(self.model_file, env=None)
            elif algo == 'SAC':
                model = SAC.load(self.model_file, env=None)
            elif algo == 'PPO':
                model = PPO.load(self.model_file, env=None)
            else:
                raise Exception('algo set error {}'.format(algo))
            print("Model loaded successfully without space checking")
        except Exception as e:
            print("Failed to load model without env: {}".format(e))
            print("Trying to load model with custom_objects to fix action_space check...")
            try:
                if algo == 'TD3':
                    model = TD3.load(self.model_file, env=self.env, custom_objects={'action_space': self.env.action_space})
                elif algo == 'SAC':
                    model = SAC.load(self.model_file, env=self.env, custom_objects={'action_space': self.env.action_space})
                elif algo == 'PPO':
                    model = PPO.load(self.model_file, env=self.env, custom_objects={'action_space': self.env.action_space})
                else:
                    raise Exception('algo set error {}'.format(algo))
                print("Model loaded successfully with custom_objects")
            except Exception as e2:
                print("Failed to load model with custom_objects: {}".format(e2))
                raise e2
        
        return model
