# YOLO v5n 装甲车检测模型训练指南

本指南详细介绍如何训练YOLO v5n模型来检测识别自定义的装甲车模型，并集成到无人机导航避障系统中。

## 1. 环境准备

### 1.1 快速安装
使用项目提供的自动安装脚本：
```bash
# 运行自动安装脚本
python install_target_detection.py
```

### 1.2 手动安装依赖
如果自动安装失败，可以手动安装：
```bash
# 安装YOLOv5
pip install yolov5

# 安装其他依赖
pip install torch torchvision opencv-python pillow

# 或者从源码安装YOLOv5
git clone https://github.com/ultralytics/yolov5
cd yolov5
pip install -r requirements.txt
```

### 1.3 验证安装
```python
import yolov5
model = yolov5.load('yolov5n.pt')
print("YOLOv5 安装成功!")

# 验证项目集成
from scripts.utils.target_detection import ArmoredVehicleDetector
detector = ArmoredVehicleDetector()
print("项目集成验证成功!")
```

## 2. 数据集准备

### 2.1 数据收集
在AirSim中收集装甲车图像数据：

```python
import airsim
import cv2
import numpy as np
import os

def collect_training_data():
    client = airsim.MultirotorClient()
    client.confirmConnection()
    
    # 创建数据目录
    os.makedirs("training_data/images", exist_ok=True)
    
    for i in range(1000):  # 收集1000张图像
        # 获取图像
        responses = client.simGetImages([
            airsim.ImageRequest("0", airsim.ImageType.Scene, False, False)
        ])
        
        if len(responses) > 0:
            img_1d = np.fromstring(responses[0].image_data_uint8, dtype=np.uint8)
            img_rgb = img_1d.reshape(responses[0].height, responses[0].width, 3)
            
            # 保存图像
            cv2.imwrite(f"training_data/images/image_{i:06d}.jpg", 
                       cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR))
        
        # 改变无人机位置和角度以获得不同视角
        # ... 添加位置变化代码
```

### 2.2 数据标注
使用标注工具（推荐LabelImg或Roboflow）标注装甲车：

1. **安装LabelImg**:
```bash
pip install labelImg
labelImg
```

2. **标注格式**: 使用YOLO格式
   - 每张图像对应一个.txt文件
   - 格式: `class_id center_x center_y width height`
   - 坐标值为相对坐标（0-1之间）

3. **标注示例**:
```
0 0.5 0.3 0.2 0.4
```
表示类别0（装甲车），中心点在(0.5, 0.3)，宽度0.2，高度0.4

### 2.3 数据集结构
```
dataset/
├── images/
│   ├── train/
│   │   ├── image_000001.jpg
│   │   ├── image_000002.jpg
│   │   └── ...
│   └── val/
│       ├── image_001001.jpg
│       └── ...
├── labels/
│   ├── train/
│   │   ├── image_000001.txt
│   │   ├── image_000002.txt
│   │   └── ...
│   └── val/
│       ├── image_001001.txt
│       └── ...
└── data.yaml
```

### 2.4 创建data.yaml配置文件
```yaml
# data.yaml
train: dataset/images/train
val: dataset/images/val

nc: 1  # 类别数量
names: ['armored_vehicle']  # 类别名称
```

## 3. 模型训练

### 3.1 训练脚本
```python
import yolov5

# 训练模型
model = yolov5.train(
    data='dataset/data.yaml',
    imgsz=640,
    epochs=100,
    batch_size=16,
    weights='yolov5n.pt',  # 使用预训练权重
    device='cuda' if torch.cuda.is_available() else 'cpu'
)
```

### 3.2 命令行训练
```bash
python train.py --data dataset/data.yaml --weights yolov5n.pt --img 640 --epochs 100 --batch-size 16
```

### 3.3 训练参数说明
- `--data`: 数据集配置文件
- `--weights`: 预训练权重文件
- `--img`: 输入图像尺寸
- `--epochs`: 训练轮数
- `--batch-size`: 批次大小
- `--device`: 设备选择（cuda/cpu）

## 4. 模型优化

### 4.1 数据增强
在训练时自动应用数据增强：
- 随机旋转
- 随机缩放
- 颜色变换
- 随机裁剪

### 4.2 超参数调优
```yaml
# hyp.yaml
lr0: 0.01
momentum: 0.937
weight_decay: 0.0005
warmup_epochs: 3.0
box: 0.05
cls: 0.5
obj: 1.0
```

### 4.3 模型验证
```python
# 验证模型性能
results = model.val()
print(f"mAP@0.5: {results.maps[0]}")
print(f"mAP@0.5:0.95: {results.maps[1]}")
```

## 5. 模型部署

### 5.1 保存训练好的模型
```python
# 保存模型
model.save('armored_vehicle_detector.pt')
```

### 5.2 在项目中使用
将训练好的模型集成到项目中：

```python
from scripts.utils.target_detection import ArmoredVehicleDetector

# 使用自定义模型
detector = ArmoredVehicleDetector(
    model_path='models/armored_vehicle_detector.pt',
    confidence_threshold=0.5
)

# 设置相机参数（匹配AirSim设置）
detector.set_camera_parameters(
    width=256,
    height=144,
    fov_degrees=90
)
```

### 5.3 配置文件集成
在配置文件中添加模型路径：

```ini
# config/config.ini
[target_detection]
model_path = models/armored_vehicle_detector.pt
confidence_threshold = 0.5
collision_speed = 5.0
detection_distance = 15.0
```

## 6. 性能优化建议

### 6.1 数据质量
- 收集多样化的数据（不同光照、角度、距离）
- 确保标注准确性
- 数据集大小建议1000+张图像

### 6.2 训练技巧
- 使用预训练权重加速收敛
- 适当的学习率调度
- 早停机制防止过拟合

### 6.3 模型选择
- YOLOv5n: 速度快，精度中等
- YOLOv5s: 平衡速度和精度
- YOLOv5m/l/x: 精度高，速度较慢

## 7. 常见问题

### 7.1 训练不收敛
- 检查学习率是否过大
- 确认数据标注正确性
- 增加训练数据量

### 7.2 检测精度低
- 增加训练轮数
- 使用更大的模型
- 改善数据质量

### 7.3 推理速度慢
- 使用较小的模型（YOLOv5n）
- 降低输入图像分辨率
- 使用GPU加速

## 8. 集成到项目

### 8.1 模型部署
将训练好的模型文件放置在项目目录中：

```
项目根目录/
├── models/
│   └── armored_vehicle_detector.pt  # 你的自定义模型
├── scripts/
│   ├── navigation_with_target_detection.py  # 综合测试脚本
│   └── test_target_detection.py            # 独立测试脚本
└── config/
    └── config.ini  # 配置文件
```

### 8.2 配置文件设置
在配置文件中添加目标检测相关参数：

```ini
# config/config.ini
[target_detection]
model_path = models/armored_vehicle_detector.pt
confidence_threshold = 0.5
collision_speed = 5.0
detection_distance = 15.0
```

### 8.3 测试自定义模型
使用以下脚本测试你的自定义模型：

```bash
# 独立测试目标检测功能
python scripts/test_target_detection.py

# 综合测试（导航+检测+撞击）
python scripts/navigation_with_target_detection.py

# 原始导航测试（不含目标检测）
python scripts/start_evaluate_with_plot.py
```

### 8.4 模型性能验证
验证模型在AirSim环境中的表现：

```python
# 在scripts/test_target_detection.py中查看检测结果
# 检查以下指标：
# - 检测准确率
# - 检测速度
# - 3D坐标恢复精度
# - 撞击成功率
```

## 9. 高级功能

### 9.1 多模型支持
项目支持在运行时切换不同的YOLO模型：

```python
# 在代码中动态切换模型
detector.load_model('models/model_v1.pt')  # 切换到版本1
detector.load_model('models/model_v2.pt')  # 切换到版本2
```

### 9.2 实时训练数据收集
利用AirSim环境收集更多训练数据：

```python
# 使用现有的数据收集功能
# 在不同环境条件下收集数据：
# - 不同光照条件
# - 不同天气条件
# - 不同飞行高度和角度
```

这样就完成了自定义装甲车检测模型的训练、部署和集成！
