.. _base_algo:

.. automodule:: stable_baselines3.common.base_class


Base RL Class
=============

Common interface for all the RL algorithms

.. autoclass:: BaseAlgorithm
  :members:


.. automodule:: stable_baselines3.common.off_policy_algorithm


Base Off-Policy Class
^^^^^^^^^^^^^^^^^^^^^

The base RL algorithm for Off-Policy algorithm (ex: SAC/TD3)

.. autoclass:: OffPolicyAlgorithm
  :members:


.. automodule:: stable_baselines3.common.on_policy_algorithm


Base On-Policy Class
^^^^^^^^^^^^^^^^^^^^^

The base RL algorithm for On-Policy algorithm (ex: A2C/PPO)

.. autoclass:: OnPolicyAlgorithm
  :members:
