# UAV Navigation DRL AirSim - 稳定版

一个用于在复杂未知环境中训练无人机导航策略的平台，现已集成稳定性增强功能。

- 基于 OpenAI Gym 创建的环境，包含多旋翼和固定翼无人机的运动学模型
- 提供多个 UE4 环境用于训练和测试导航策略
- 基于 AirSim 和 Stable-Baselines3 (SB3)
- **新增：稳定性管理器，解决长时间训练中的连接问题**

训练你自己的自主飞行策略，甚至可以迁移到真实无人机上！玩得开心 ^_^!

<p align="center">
  <img src="resources/figures/result_3d_NH_simple_dynamics.gif" width = "400" height = "225"/>
  <img src="resources/figures/result_3d_NH_multirotor.gif" width = "400" height = "225"/>
</p>

## 🆕 最新更新 (2024-2025)

### 🎯 目标检测与撞击系统 (2025.1)
- ✅ **YOLO v5n 目标检测**：集成轻量级YOLO模型进行装甲车检测
- ✅ **3D坐标恢复**：结合RGB和深度图像精确恢复目标3D位置
- ✅ **智能阶段切换**：距离目标15米时自动启用检测模式
- ✅ **直线撞击控制**：检测到目标后以5m/s速度直线撞击
- ✅ **模块化设计**：与原导航系统完全解耦，支持独立运行
- ✅ **向后兼容**：原有导航脚本无需修改即可继续使用
- ✅ **完整测试套件**：独立测试脚本验证检测和撞击功能

### 🚀 训练系统重大升级 (2024.12)
- ✅ **智能早停机制**：每30000步自动检测成功率变化，防止过度训练
- ✅ **高频检查点保存**：从10000步优化为5000步保存频率
- ✅ **最佳模型自动选择**：训练结束时自动保存成功率最高的检查点为最终模型
- ✅ **安全继续训练**：所有继续训练都创建新文件夹，完全保护原始模型
- ✅ **智能模型选择**：继续训练时优先选择最终模型，备选最新检查点
- ✅ **完整训练记录**：自动保存配置文件和训练信息，便于追溯
- ✅ **性能优化**：直接使用训练统计数据，无需重新评估，零额外开销

### 稳定性增强功能
- ✅ **AirSim 连接稳定性管理器**：自动处理长时间训练中的连接问题
- ✅ **智能重试机制**：图像获取失败时自动重试和重连
- ✅ **训练自动重启**：训练崩溃时自动重启（最多3次）
- ✅ **详细错误日志**：完整的错误记录和调试信息
- ✅ **自动检查点保存**：每5000步自动保存，训练中断可恢复
- ✅ **智能模型管理**：自动查找最新检查点或最终模型
- ✅ **继续训练功能**：从任意检查点无缝继续训练

### 解决的主要问题
- 🔧 修复了 `msgpackrpc.error.RPCError: bad cast` 错误
- 🔧 解决了长时间训练（>10000步）后的连接不稳定问题
- 🔧 解决了训练中断导致进度丢失的问题
- 🔧 改进了错误恢复和处理机制
- 🔧 **新增**：解决了过度训练导致性能下降的问题
- 🔧 **新增**：解决了继续训练可能覆盖好模型的风险
- 🔧 **最新**：解决了新旧PyTorch版本模型兼容性问题
- 🔧 **最新**：增强了AirSim连接恢复机制和错误处理
- 🔧 **最新**：修复了目标检测模块缺失的 `detect_and_get_3d_positions` 方法

## 🚀 快速开始

### 1. 环境准备

**系统要求：**
- Python 3.8
- [AirSim](https://microsoft.github.io/AirSim/) v1.6.0
- PyTorch 1.10.1 with GPU
- Windows 10/11 (推荐)

### 2. 安装步骤

```bash
# 1. 克隆仓库（包含子模块）
git clone https://github.com/heleidsn/UAV_Navigation_DRL_AirSim.git --recursive
cd UAV_Navigation_DRL_AirSim

# 2. 安装 gym_env
cd gym_env
pip install msgpack msgpack-rpc-python
pip install -e .

# 3. 安装定制的 stable-baselines3
cd ../stable-baselines3
pip install -e .

# 4. 安装其他依赖
pip install wandb pyqtgraph seaborn keyboard tensorboard tqdm

# 5. 返回项目根目录
cd ..
```

### 3. 下载 AirSim 环境

下载 [SimpleAvoid 环境](https://drive.google.com/file/d/1QgkZY5-GXRr93QTV-s2d2OCoVSndADAM/view?usp=sharing) 并解压。

### 4. 配置 AirSim

将以下配置保存为 `Documents/AirSim/settings.json`：

```json
{
  "SeeDocsAt": "https://github.com/Microsoft/AirSim/blob/master/docs/settings.md",
  "SettingsVersion": 1.2,
  "SimMode": "Multirotor",
  "ViewMode": "",
  "ClockSpeed": 10,
  "CameraDefaults": {
    "CaptureSettings": [
      {
        "ImageType": 3,
        "Width": 100,
        "Height": 80,
        "FOV_Degrees": 90
      }
    ]
  }
}
```

## 🎯 使用方法

### 🚀 一键开始训练

**推荐方式（稳定版）：**
```bash
# 1. 启动 AirSim 环境（SimpleAvoid.exe）
# 2. 运行稳定训练脚本
cd scripts
python start_train_with_plot_stable.py
```

**标准方式：**
```bash
cd scripts
python start_train_with_plot.py
```

### 📋 完整训练流程

#### 步骤 1: 启动 AirSim 环境
1. 运行下载的 `SimpleAvoid.exe`
2. 等待环境加载完成
3. 可以按 `Alt+Enter` 退出全屏模式

#### 步骤 2: 开始训练
```bash
python scripts\start_train_with_plot_stable.py
```

#### 步骤 3: 监控训练
- 观察 GUI 界面中的实时数据
- 检查控制台输出
- 查看 `logs/training_errors.log` 了解详细信息

#### 步骤 4: 训练过程中
- ✅ **智能检查点保存**：每5000步自动保存到 `logs/[环境]/[时间]/models/checkpoints/`
- ✅ **高效早停监控**：每30000步检测成功率变化，使用训练统计数据，无额外开销
- ✅ **实时监控**：观察控制台输出的保存信息和成功率变化
- ✅ **错误恢复**：如果训练中断，会从最近检查点自动恢复
- ✅ **最佳模型跟踪**：自动记录成功率最高的检查点，基于实际训练表现

#### 步骤 5: 训练完成
- **最佳模型自动保存**：成功率最高的检查点自动保存为 `model_sb3.zip`
- 检查点保存到 `logs/[环境]/[时间戳]/models/checkpoints/checkpoint_X_steps.zip`
- 训练日志保存到 `logs/` 目录
- 早停信息：如果触发早停，会显示最佳检查点信息

### 🔄 安全继续训练

**重要特性**：所有继续训练都会创建新文件夹，**完全保护原始模型不被覆盖**！

#### **推荐方式1：带GUI可视化继续训练**：
```bash
# 自动找到最新模型，带完整GUI界面（推荐）
python scripts/continue_training_with_plot.py

# 指定特定模型文件，带GUI界面
python scripts/continue_training_with_plot.py -model_path "logs/SimpleAvoid/2025_06_22_00_35_Multirotor_No_CNN_SAC/models/checkpoints/checkpoint_25000_steps.zip" -additional_steps 80000
```

#### **推荐方式2：命令行增强稳定版**：
```bash
# 自动找到最新模型并安全继续训练
python scripts/continue_training_stable.py

# 指定训练步数
python scripts/continue_training_stable.py -additional_steps 30000

# 指定特定模型文件
python scripts/continue_training_stable.py -model_path "logs/SimpleAvoid/2025_06_22_00_35_Multirotor_No_CNN_SAC/models/checkpoints/checkpoint_25000_steps.zip"
```


**继续训练的安全机制**：
- ✅ **原始模型受保护**：永远不会被覆盖
- ✅ **智能模型选择**：优先选择 `model_sb3.zip`，备选最新检查点
- ✅ **独立文件夹**：每次继续训练都创建新的时间戳文件夹
- ✅ **完整记录**：自动保存配置文件和训练信息
- ✅ **可追溯性**：通过 `training_info.txt` 可追踪到原始模型
- ✅ **兼容性保证**：自动处理新旧PyTorch版本模型兼容性
- ✅ **稳定性增强**：集成AirSim恢复机制，训练更稳定
- ✅ **GUI可视化**：实时监控训练进度和性能指标
- ✅ **环境分类存储**：按训练环境分类存放，便于管理

### 📊 智能检查点管理

```bash
cd scripts

# 查看所有保存的模型和检查点
python checkpoint_manager.py --list-all

# 只查看检查点
python checkpoint_manager.py --list

# 查看最新检查点
python checkpoint_manager.py --latest

# 清理旧检查点，只保留最新5个
python checkpoint_manager.py --clean 5

# 测试训练改进功能
python test_training_improvements.py --test all
```

**检查点管理特性**：
- 🔍 **智能查找**：自动识别最终模型和检查点
- 📊 **详细信息**：显示步数、时间、路径等信息
- 🧹 **自动清理**：可设置保留数量，自动删除旧检查点
- ✅ **功能测试**：提供测试脚本验证所有功能

### 📊 模型评估和测试


# 使用GUI评估训练好的模型
```bash
python scripts\start_evaluate_with_plot.py
```

### 🎮 训练脚本对比

| 特性 | `start_train_with_plot.py` | `start_train_with_plot_stable.py` | `continue_training_with_plot.py` | `continue_training_stable.py` |
|------|---------------------------|-----------------------------------|----------------------------------|------------------------------|
| **GUI可视化** | ✅ 完整界面 | ✅ 完整界面 | ✅ 完整界面 | ❌ 命令行 |
| **稳定性修复** | ✅ 已集成 | ✅ 已集成 | ✅ 已集成 | ✅ 已集成 |
| **自动重启** | ❌ 无 | ✅ 最多5次重启 | ❌ 无 | ❌ 无 |
| **兼容性修复** | ❌ 无 | ❌ 无 | ✅ 新旧PyTorch | ✅ 新旧PyTorch |
| **环境分类存储** | ❌ 无 | ❌ 无 | ✅ 按环境分类 | ✅ 按环境分类 |
| **详细日志** | ⚠️ 基础日志 | ✅ 保存到文件 | ✅ 保存到文件 | ✅ 保存到文件 |
| **推荐场景** | 短期训练/调试 | **长期训练** | **继续训练（推荐）** | 继续训练/服务器 |

## 📁 项目结构

```
UAV_Navigation_DRL_AirSim/
├── configs/                          # 配置文件
│   ├── config_Maze_SimpleMultirotor_2D.ini      # 原始配置
│   └── config_Maze_SimpleMultirotor_2D_stable.ini # 稳定版配置
├── scripts/                          # 训练脚本
│   ├── start_train_with_plot.py      # 标准训练脚本
│   ├── start_train_with_plot_stable.py # 稳定训练脚本（推荐）
│   ├── continue_training.py          # 安全继续训练脚本
│   ├── continue_training_stable.py   # 增强版继续训练脚本（推荐）
│   ├── load_model_example.py         # 模型加载示例
│   ├── checkpoint_manager.py         # 检查点管理工具
│   ├── test_training_improvements.py # 训练功能测试脚本
│   └── utils/
│       ├── airsim_stability_manager.py # 稳定性管理器
│       ├── airsim_recovery_manager.py  # AirSim恢复管理器
│       ├── thread_train.py           # 训练线程（含早停和智能检查点）
│       └── thread_evaluation.py     # 评估线程（含兼容性修复）
├── gym_env/                          # 自定义 Gym 环境
├── stable-baselines3/                # 定制的 SB3
├── check_training_setup.py          # 训练环境检查工具
├── test_airsim_stability.py         # AirSim稳定性测试工具
├── docs/                            # 文档目录
│   └── training_improvements.md     # 训练改进详细说明
├── logs/                            # 训练日志和模型
│   └── [环境名]/
│       ├── [时间戳]_原始训练/
│       │   ├── models/
│       │   │   ├── model_sb3.zip    # 最佳模型（受保护）
│       │   │   └── checkpoints/     # 检查点目录（每5000步）
│       │   │       ├── checkpoint_5000_steps.zip
│       │   │       ├── checkpoint_10000_steps.zip
│       │   │       └── ...
│       │   ├── config/config.ini    # 配置文件副本
│       │   └── tb_logs/             # TensorBoard日志
│       └── [时间戳]_continued_from_final_model/  # 继续训练（新文件夹）
│           ├── models/
│           │   ├── model_sb3.zip    # 新的最佳模型
│           │   └── checkpoints/     # 新的检查点
│           ├── config/config.ini    # 配置文件副本
│           └── training_info.txt    # 训练信息记录
└── resources/                       # 资源文件
```

## 🔧 兼容性和恢复机制

### 🔄 PyTorch版本兼容性

**问题背景**：
- 旧版PyTorch 1.10.1训练的模型在新版PyTorch 2.7.1环境中可能无法直接加载
- 新版PyTorch 2.7.1训练的模型格式更灵活，兼容性更好

**解决方案**：
项目现已集成**双重兼容性加载机制**，可以同时运行：
- ✅ **旧版模型**（PyTorch 1.10.1训练的模型）
- ✅ **新版模型**（PyTorch 2.7.1训练的模型）

**工作原理**：
```python
# 第一步：尝试新版本加载方式
model = SAC.load(model_path, env=None)  # 适用于新版模型

# 第二步：如果失败，使用兼容性方式
model = SAC.load(model_path, env=env, custom_objects={'action_space': env.action_space})  # 适用于旧版模型

# 第三步：手动设置环境
model.set_env(env)  # 确保模型与环境正确关联
```

### 🛡️ AirSim连接恢复机制

**问题背景**：
- AirSim长时间训练容易出现 `msgpackrpc.error.RPCError: bad cast` 错误
- 图像请求在高频率下容易失败
- 连接不稳定导致训练中断

**解决方案**：
集成了**多层次恢复机制**：

#### **1. 智能重试机制**
```python
# 增强的图像获取函数
def get_depth_image(self):
    # 使用恢复管理器安全执行
    return self.recovery_manager.safe_execute(image_operation, "get_depth_image")
```

#### **2. 自动连接恢复**
- **连接健康检查**：定期验证AirSim连接状态
- **自动重连**：检测到连接问题时自动重新建立连接
- **紧急重置**：最后手段的环境重置机制

#### **3. 训练级别恢复**
- **自动重启**：训练崩溃时自动重启（最多5次）
- **智能等待**：根据错误类型调整等待时间
- **错误分类处理**：特别处理AirSim RPC错误

### 📋 使用增强功能

#### **检查环境设置**：
```bash
# 检查训练环境配置
python check_training_setup.py

# 测试AirSim稳定性修复
python test_airsim_stability.py
```

#### **使用稳定版脚本**：
```bash
# 稳定版训练（推荐）
python scripts/start_train_with_plot_stable.py

# 稳定版继续训练（推荐）
python scripts/continue_training_stable.py
```

### 🎯 兼容性特性总结

| 特性 | 标准版本 | 增强版本 |
|------|---------|---------|
| **PyTorch兼容性** | ❌ 单一版本 | ✅ 新旧版本通用 |
| **AirSim恢复** | ⚠️ 基础重试 | ✅ 多层次恢复 |
| **错误处理** | ⚠️ 简单处理 | ✅ 智能分类处理 |
| **训练稳定性** | ⚠️ 手动重启 | ✅ 自动恢复 |
| **模型加载** | ❌ 可能失败 | ✅ 100%兼容 |

## ⚙️ 配置说明

### 训练环境 (env_name)

- **SimpleAvoid**: 简单避障环境（推荐新手）
- **City_400**: 城市环境
- **NH_center**: AirSim 内置环境
- **Trees**: 森林环境

### 动力学模型 (dynamic_name)

- **SimpleMultirotor**: 简化多旋翼模型（推荐）
- **Multirotor**: 完整多旋翼模型
- **SimpleFixedwing**: 简化固定翼模型

### 算法 (algo)

- **SAC**: Soft Actor-Critic（推荐）
- **TD3**: Twin Delayed DDPG
- **PPO**: Proximal Policy Optimization

### 训练改进配置

在 `config_Maze_SimpleMultirotor_2D_stable.ini` 中：

```ini
[stability]
health_check_interval = 1000      # 连接健康检查间隔
max_reconnect_attempts = 3        # 最大重连尝试次数
max_image_retries = 5            # 图像请求最大重试次数
enable_checkpoint_save = True     # 启用检查点保存
checkpoint_save_interval = 5000   # 检查点保存间隔（优化为5000步）

[checkpoint]
save_freq = 5000                 # 检查点保存频率（优化为5000步）
keep_checkpoints = 10            # 保留的检查点数量
name_prefix = checkpoint         # 检查点文件名前缀
verbose = True                   # 显示保存信息

# 新增：早停配置
[early_stopping]
check_freq = 30000               # 早停检查频率（每30000步）
patience_threshold = 0.0         # 容忍阈值（0表示不允许成功率下降）
eval_episodes = 50              # 每次评估使用的episode数量
```

## 🔧 故障排除

### ❗ 常见问题及解决方案

#### 1. **"bad cast" 错误**
```
msgpackrpc.error.RPCError: rpclib: function 'simGetImages' threw an exception: bad cast
```
**解决方案：**
- ✅ 已通过增强恢复机制自动解决
- 使用 `start_train_with_plot_stable.py` 或 `continue_training_stable.py` 脚本
- 系统会自动重试和恢复，无需手动干预
- 如果仍有问题，运行 `python test_airsim_stability.py` 检查连接

#### 2. **训练突然中断**
**解决方案：**
- ✅ 稳定版脚本会自动重启（最多3次）
- 检查 `logs/training_errors.log` 查看详细错误信息
- 确保 AirSim 环境正常运行

#### 3. **连接不稳定或超时**
**解决方案：**
- 重启 AirSim 环境
- 检查系统内存使用情况（建议 >8GB 可用内存）
- 降低 `ClockSpeed` 设置（从 10 降到 5 或 1）
- 关闭其他占用 GPU 的程序

#### 4. **训练速度慢**
**解决方案：**
- 设置 `ClockSpeed` 为 10-20（在 settings.json 中）
- 使用 SSD 存储 AirSim 环境文件
- 设置 `ViewMode` 为 `NoDisplay`
- 确保使用 GPU 训练

#### 5. **GPU 内存不足**
**解决方案：**
- 降低 `batch_size`（从 512 降到 256 或 128）
- 减小 `buffer_size`
- 关闭其他使用 GPU 的程序

#### 6. **模型不收敛**
**解决方案：**
- 检查奖励函数设计
- 调整学习率（尝试 1e-4 或 3e-4）
- 增加训练步数
- 检查环境设置是否正确

#### 7. **模型加载失败**
```
ValueError: Action space mismatch
```
**解决方案：**
- ✅ 已通过兼容性机制自动解决
- 使用 `continue_training_stable.py` 脚本
- 系统会自动处理新旧PyTorch版本兼容性
- 运行 `python check_training_setup.py` 验证环境配置

#### 8. **训练环境检查**
**快速诊断：**
```bash
# 全面检查训练环境
python check_training_setup.py

# 测试AirSim连接稳定性
python test_airsim_stability.py
```

### 📁 重要文件位置

- **错误日志**: `logs/training_errors.log`
- **训练模型**: `logs/[环境名]/[时间戳]/models/model_sb3.zip`
- **检查点**: `logs/[环境名]/[时间戳]/models/checkpoints/`
- **继续训练模型**: `logs/[时间戳]_continued_from_*/models/model_sb3.zip`
- **训练信息**: `logs/[时间戳]_continued_from_*/training_info.txt`
- **TensorBoard 日志**: `logs/[环境名]/[时间戳]/tb_logs/`
- **AirSim 配置**: `Documents/AirSim/settings.json`

### 🔍 调试技巧

1. **查看实时日志**:
   ```bash
   tail -f logs/training_errors.log
   ```

2. **检查 GPU 使用情况**:
   ```bash
   nvidia-smi
   ```

3. **测试 AirSim 连接**:
   ```python
   import airsim
   client = airsim.MultirotorClient()
   client.confirmConnection()
   print("Connection OK!")
   ```

## 📊 训练监控

### GUI 界面

训练过程中会显示实时监控界面：
- 动作曲线
- 状态信息
- 奖励曲线
- 轨迹可视化

### WandB 支持

启用 WandB 监控：

```ini
[options]
use_wandb = True

[wandb]
name = your_experiment_name
notes = experiment_description
```

## 🎯 训练技巧

### 1. 参数调优

- **学习率**: 从 1e-3 开始
- **批次大小**: 512（推荐）
- **缓冲区大小**: 50000
- **训练频率**: 100

### 2. 稳定训练

- 使用稳定版训练脚本
- 定期保存检查点
- 监控系统资源使用

### 3. 模型评估

- 训练完成后使用确定性策略评估
- 测试多个随机种子
- 记录成功率和平均奖励

## 🌟 高级功能

### 🎨 自定义环境

1. **添加新环境**:
   ```python
   # 在 gym_env/gym_env/envs/airsim_env.py 中添加
   elif self.env_name == 'YourCustomEnv':
       start_position = [0, 0, 5]
       goal_distance = 50
       # ... 环境配置
   ```

2. **更新配置文件**:
   ```ini
   [options]
   env_name = YourCustomEnv
   ```

3. **测试环境连接**

### 🎯 自定义奖励函数

在 `airsim_env.py` 中修改奖励函数：

```python
def compute_reward_custom(self, done, action):
    reward = 0
    if not done:
        # 距离奖励
        distance_reward = ...
        # 避障奖励
        obstacle_penalty = ...
        # 动作平滑性
        action_penalty = ...

        reward = distance_reward - obstacle_penalty - action_penalty
    return reward
```

### 🚁 模型部署

训练好的模型可以：

1. **仿真环境评估**:
   ```python
   model = SAC.load("path/to/model.zip")
   obs = env.reset()
   action, _ = model.predict(obs, deterministic=True)
   ```

2. **真实无人机部署**:
   - 使用 PX4 或 ArduPilot 固件
   - 通过 MAVLink 协议通信
   - 实时传感器数据处理

3. **进一步强化学习**:
   - 作为预训练模型
   - 迁移学习到新环境
   - 多任务学习

### 📊 实验管理

#### WandB 集成
```ini
[options]
use_wandb = True

[wandb]
name = experiment_name
notes = experiment_description
```

#### 超参数调优
```python
# 在配置文件中调整
learning_rate = 1e-3  # 学习率
batch_size = 512      # 批次大小
buffer_size = 50000   # 经验回放缓冲区
```

## 📚 参考资料和文档

### 📖 官方文档
- [AirSim 官方文档](https://microsoft.github.io/AirSim/)
- [Stable-Baselines3 文档](https://stable-baselines3.readthedocs.io/)
- [OpenAI Gym 文档](https://gym.openai.com/)

### 📋 项目相关
- [训练改进功能详细说明](docs/training_improvements.md)
- [性能优化说明](docs/performance_optimization.md)
- [训练改进总结](TRAINING_IMPROVEMENTS_SUMMARY.md)
- [稳定性修复详细说明](AIRSIM_STABILITY_FIX.md)
- [原始 README](README_old.md)

### 🎓 学习资源
- [强化学习入门](https://spinningup.openai.com/)
- [深度强化学习算法](https://stable-baselines3.readthedocs.io/en/master/guide/algos.html)

## ❓ 常见问题 FAQ

**Q: 训练需要多长时间？**
A: 通常 50,000-100,000 步，根据环境复杂度和硬件配置，大约 2-8 小时。现在有早停机制，可能会提前结束。

**Q: 需要什么样的硬件配置？**
A: 推荐 GTX 1060 以上 GPU，8GB+ 内存，SSD 存储。

**Q: 可以在 Linux 上运行吗？**
A: AirSim 主要支持 Windows，Linux 支持有限。

**Q: 如何提高训练成功率？**
A: 使用稳定版脚本，调整奖励函数，利用早停机制防止过度训练。

**Q: 早停机制会不会过早停止训练？**
A: 可以调整 `patience_threshold` 参数，允许小幅成功率下降。

**Q: 继续训练会覆盖原来的好模型吗？**
A: 不会！所有继续训练都创建新文件夹，原始模型完全受保护。

**Q: 如何知道哪个是最佳模型？**
A: 系统会自动将成功率最高的检查点保存为 `model_sb3.zip`。

**Q: 成功率评估会影响训练速度吗？**
A: 不会！系统直接使用训练过程中的统计数据，无需重新运行episode，零额外开销。

## 🤝 贡献指南

### 🐛 报告问题
1. 检查现有 Issues
2. 提供详细的错误信息和日志
3. 包含系统配置信息

### 🔧 提交代码
1. Fork 项目
2. 创建功能分支
3. 提交 Pull Request
4. 确保代码通过测试

### 📝 改进文档
- 修正错误
- 添加示例
- 翻译文档

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有贡献者和开源社区的支持！

## 🎯 训练改进功能快速参考

### 🚀 新功能亮点
- **早停机制**：每30000步检测成功率，自动停止过度训练
- **高频检查点**：每5000步保存，更精细的训练进度控制
- **最佳模型选择**：自动保存成功率最高的检查点为最终模型
- **安全继续训练**：创建新文件夹，保护原始模型不被覆盖

### 📋 快速命令
```bash
# 检查训练环境（推荐首次运行）
python check_training_setup.py

# 测试AirSim稳定性
python test_airsim_stability.py

# 开始训练（带早停和智能检查点）
python scripts/start_train_with_plot_stable.py

# 安全继续训练（带GUI，推荐）
python scripts/continue_training_with_plot.py

# 安全继续训练（命令行增强版）
python scripts/continue_training_stable.py

# 安全继续训练（命令行标准版）
python scripts/continue_training.py

# 查看所有模型和检查点
python scripts/checkpoint_manager.py --list-all

# 测试新功能
python scripts/test_training_improvements.py
```

### 📁 文件结构说明
- `logs/[环境]/[时间]/models/model_sb3.zip` - 最佳模型（成功率最高）
- `logs/[环境]/[时间]/models/checkpoints/` - 检查点（每5000步）
- `logs/[时间]_continued_from_*/` - 继续训练结果（新文件夹）

---

**🚁 享受训练你的无人机导航策略！Happy Flying! ✨**

> 如果这个项目对你有帮助，请给个 ⭐ Star！


个人记录（以下的成功率指最近100轮的成功率）：
## SimpleAvoid环境：

pytorch 1.10.1

1、在configs/config_Maze_SimpleMultirotor_2D_stable.ini中使用SAC和No_CNN时，总步数设置为100000，screen_height = 80，screen_width = 100，cnn_feature_num = 25(成功率0.95)；（logs\SimpleAvoid\2025_06_14_04_09_Multirotor_No_CNN_SAC）
2、使用SAC和CNN_GAP时，总步数设置为150000，screen_height = 80，screen_width = 100，cnn_feature_num = 72(成功率0.06)；
3、使用PPO和No_CNN时，总步数设置为150000，screen_height = 80，screen_width = 100，cnn_feature_num = 25(成功率0)；
4、使用PPO和CNN_GAP时，总步数设置为150000，screen_height = 80，screen_width = 100，cnn_feature_num = 25(成功率0)；
5、使用SAC和CNN_GAP时，总步数设置为150000，screen_height = 80，screen_width = 100，更改CNN_GAP_new架构(bool为 3，3)调整cnn输出特征数后:cnn_feature_num = 48(成功率0.35 最近20轮成功率0.45)
6、使用SAC和CNN_GAP时，总步数设置为150000，screen_height = 80，screen_width = 100，更改CNN_GAP_new架构(bool为 4，5)调整cnn输出特征数后:cnn_feature_num = 32(成功率0)
7、使用SAC和CNN_GAP时，总步数设置为100000，screen_height = 80，screen_width = 100，将使用CNN_GAP_new架构更改为使用CNN_GAP，cnn_feature_num = 8(成功率0)
（另一台电脑上训练结果）使用SAC和CNN_GAP时，总步数设置为100000，screen_height = 80，screen_width = 100，更改CNN_GAP_new架构(bool为 3，4)调整cnn输出特征数后:cnn_feature_num = 32(成功率0)

pytorch 2.7.1

1、在configs/config_Maze_SimpleMultirotor_2D_stable.ini中使用SAC和No_CNN时，总步数设置为105000（之前训练25000步后连接中断了），screen_height = 80，screen_width = 100，cnn_feature_num = 25(成功率0.93,100000步后成功率开始下降)；(logs\SimpleAvoid\2025_06_22_02_58_Multirotor_No_CNN_SAC_continued_from_checkpoint)


## NH_center环境：

pytorch 2.7.1

1、在configs\config_NH_center_SimpleMultirotor_3D.ini中使用SAC和No_CNN时，停止步数为120000（由于成功率相对于前3万步下降所以早停了），screen_height = 80，screen_width = 100，cnn_feature_num = 25(最高成功率0.46)；(logs\NH_center\2025_06_23_12_09_Multirotor_No_CNN_SAC)
2、在configs\config_NH_center_SimpleMultirotor_3D.ini中使用SAC和No_CNN_64时，停止步数为180000，screen_height = 80，screen_width = 100，cnn_feature_num = 64(最高成功率为0.5在80000步时后面在一直下降)；(logs\NH_center\2025_06_24_03_43_Multirotor_No_CNN_64_SAC_continued_from_checkpoint)
3、在configs\config_NH_center_SimpleMultirotor_3D.ini中使用SAC和No_CNN_100时，停止步数为170000，screen_height = 80，screen_width = 100，cnn_feature_num = 100(最高成功率为0.5在140000步时后面在一直下降)；(logs\NH_center\2025_06_25_00_08_Multirotor_No_CNN_100_SAC)
3、在configs\config_NH_center_SimpleMultirotor_3D.ini中使用SAC和No_CNN_mix时，停止步数为200000，screen_height = 80，screen_width = 100，cnn_feature_num = 125(最高成功率为0.42在176000步时后面在一直下降)；(logs\NH_center\2025_06_25_15_35_Multirotor_No_CNN_mix_SAC)

## My_tree_1环境：
1、在configs\config_My_tree_1_SimpleMultirotor.ini中使用SAC和No_CNN时，停止步数为200000，screen_height = 80，screen_width = 100，cnn_feature_num = 25(最高成功率0.97)；
2、在configs\config_My_tree_1_SimpleMultirotor.ini中使用SAC和No_CNN，并将奖励系数由50提高至500，停止步数为200000，screen_height = 80，screen_width = 100，cnn_feature_num = 25(最高成功率0.99)；(logs\My_tree_1\2025_06_30_10_12_Multirotor_No_CNN_SAC)


输入为深度图像提取的特征和状态特征：

输入的状态特征数：
6个基础特征：
 distance_norm - 到目标的水平距离（归一化）
 vertical_distance_norm - 到目标的垂直距离（归一化）
 relative_yaw_norm - 相对偏航角（归一化）
 linear_velocity_norm - 水平速度（归一化）
 linear_velocity_z_norm - 垂直速度（归一化）
 angular_velocity_norm - 偏航角速度（归一化）

根据配置选择的最终特征
2D导航 + 无速度状态（2个特征）
[distance_norm, relative_yaw_norm]
到目标的水平距离
相对偏航角

2D导航 + 有速度状态（4个特征）
[distance_norm, relative_yaw_norm, linear_velocity_norm, angular_velocity_norm]
到目标的水平距离
相对偏航角
水平速度
偏航角速度

3D导航 + 无速度状态（3个特征）
[distance_norm, vertical_distance_norm, relative_yaw_norm]
到目标的水平距离
到目标的垂直距离
相对偏航角

3D导航 + 有速度状态（6个特征）
[distance_norm, vertical_distance_norm, relative_yaw_norm, 
 linear_velocity_norm, linear_velocity_z_norm, angular_velocity_norm]
到目标的水平距离
到目标的垂直距离
相对偏航角
水平速度
垂直速度
偏航角速度

数值范围和归一化
所有状态特征和深度图像数据都被归一化到 [0, 255] 范围：


输出（动作空间）
1. 多旋翼无人机（Multirotor）

2D导航模式
动作空间: [v_xy, yaw_rate]
维度: 2
范围: [v_xy_min, -yaw_rate_max] 到 [v_xy_max, yaw_rate_max]

3D导航模式
动作空间: [v_xy, v_z, yaw_rate]
维度: 3
范围: [v_xy_min, -v_z_max, -yaw_rate_max] 到 [v_xy_max, v_z_max, yaw_rate_max]

动作含义：
 v_xy：水平速度（m/s）
 v_z：垂直速度（m/s）
 yaw_rate：偏航角速度（rad/s）



在复制项目为新项目并在新项目上继续开发，如果改变了gym_env文件夹中的内容，需要重新更新软链接到新项目，因为在新项目中使用的gym_env仍然是旧项目的gym_env地址的软链接。但是gym_env中需要安装的依赖版本无需更新，所以可以通过cd gym_env，然后直接pip install -e .或者pip install -e . --no-deps或pip install -e . --force-reinstall --no-deps来仅更新软链接，之后可以使用cat /path/to/python/site-packages/gym-env.egg-link(比如cat C:\Users\<USER>\Anaconda3\envs\airsim_navigation_detection_3.9\lib\site-packages\gym-env.egg-link)来查询软链接是否更新。当然修改了stable-baselines3文件夹中的内容也是一样的更新方式。


这些环境图片中，X轴正方向指向右，Y轴正方向指向下，Z轴正方向指向里。

### 完整常见使用方法：

```bash
# 开始训练：
python scripts\start_train_with_plot_stable.py

# 继续训练
python scripts/continue_training_with_plot.py -model_path "logs\NH_center\2025_06_24_03_43_Multirotor_No_CNN_64_SAC_continued_from_checkpoint\models\checkpoints\checkpoint_80000_steps.zip" -additional_steps 80000

# 评估
python scripts\start_evaluate_with_plot.py
```