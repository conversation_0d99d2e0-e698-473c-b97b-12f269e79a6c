"""
带GUI可视化的继续训练脚本
集成了兼容性修复、稳定性增强和实时监控界面
"""

import sys
import argparse
import os
import glob
import time
import traceback
import logging
from datetime import datetime
from configparser import ConfigParser

# 添加PyQt5支持
from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import QThread, pyqtSignal

# 导入训练相关模块
from utils.thread_train import TrainingThread
from utils.ui_train import TrainingUi

# 导入stable-baselines3
sys.path.append('../stable-baselines3')
from stable_baselines3 import SAC, TD3, PPO


def setup_error_logging():
    """设置错误日志"""
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 清除现有的handlers
    logger.handlers.clear()

    # 创建formatter（不使用emoji字符）
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 文件handler（使用UTF-8编码）
    file_handler = logging.FileHandler(
        os.path.join(log_dir, 'continue_training_errors.log'),
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 控制台handler（使用UTF-8编码）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


def get_parser():
    parser = argparse.ArgumentParser(
        description="Continue training with GUI visualization and stability enhancements")
    parser.add_argument('-config', required=False,
                        help='config file name (optional - will auto-detect from model directory if not provided)',
                        default='config_Maze_SimpleMultirotor_2D_stable')
    parser.add_argument('-model_path', required=False,
                        help='path to saved model (if not provided, will find latest)')
    parser.add_argument('-additional_steps', type=int, default=50000,
                        help='additional training steps')
    return parser


def find_config_for_model(model_path):
    """根据模型路径自动查找对应的配置文件"""
    print(f"Searching for config file for model: {model_path}")

    # 使用更直接的方法：从模型路径向上查找训练目录
    current_dir = os.path.dirname(model_path)

    # 对于检查点模型，需要向上两级目录
    if 'checkpoints' in model_path:
        # logs/env/timestamp/models/checkpoints/checkpoint.zip -> logs/env/timestamp
        training_dir = os.path.dirname(os.path.dirname(current_dir))
    else:
        # logs/env/timestamp/models/model.zip -> logs/env/timestamp
        training_dir = os.path.dirname(current_dir)

    print(f"Inferred training directory: {training_dir}")

    # 查找配置文件
    config_paths_to_try = [
        os.path.join(training_dir, 'config', 'config.ini'),  # 新格式
        os.path.join(training_dir, 'config.ini'),            # 旧格式
    ]

    print(f"Searching for config files:")
    for config_path in config_paths_to_try:
        abs_config_path = os.path.abspath(config_path)
        print(f"   Checking: {abs_config_path}")
        if os.path.exists(abs_config_path):
            print(f"✅ Found model's original config: {abs_config_path}")
            return abs_config_path
        else:
            print(f"   ❌ Not found")

    print(f"⚠️  No config file found in model directory")
    return None


def find_best_model():
    """查找最佳模型（优先选择最终模型model_sb3.zip，如果不存在则选择最新检查点）"""
    # 尝试多个可能的logs目录位置
    possible_logs_dirs = [
        'logs',     # 从项目根目录运行
        '../logs',  # 从scripts目录运行
        os.path.join(os.path.dirname(__file__), '..', 'logs')  # 相对于脚本位置
    ]

    logs_dir = None
    for dir_path in possible_logs_dirs:
        if os.path.exists(dir_path):
            logs_dir = dir_path
            break

    if logs_dir is None:
        print("No logs directory found in any of these locations:")
        for dir_path in possible_logs_dirs:
            print(f"  - {os.path.abspath(dir_path)}")
        return None, None

    print(f"Using logs directory: {os.path.abspath(logs_dir)}")

    # 首先查找最终模型（这些通常是训练完成后保存的最佳模型）
    final_model_pattern = os.path.join(logs_dir, '**/models/model_sb3.zip')
    final_models = glob.glob(final_model_pattern, recursive=True)

    if final_models:
        # 如果有多个最终模型，选择最新的
        latest_final_model = max(final_models, key=os.path.getmtime)
        print(f"Found final model: {latest_final_model}")
        return latest_final_model, 'final'

    # 如果没有最终模型，查找检查点
    checkpoint_pattern = os.path.join(logs_dir, '**/checkpoints/checkpoint_*.zip')
    checkpoints = glob.glob(checkpoint_pattern, recursive=True)

    if not checkpoints:
        print("No saved models found")
        return None, None

    # 按修改时间排序，获取最新的检查点
    latest_checkpoint = max(checkpoints, key=os.path.getmtime)

    # 从文件名中提取步数
    import re
    match = re.search(r'checkpoint_(\d+)_steps', latest_checkpoint)
    if match:
        steps = match.group(1)
        print(f"Found latest checkpoint: {latest_checkpoint} (at {steps} steps)")
    else:
        print(f"Found latest checkpoint: {latest_checkpoint}")

    return latest_checkpoint, 'checkpoint'


def load_model_with_compatibility(algo, model_path, env):
    """使用兼容性机制加载模型"""
    print(f"Loading {algo} model from {model_path}")
    
    # 兼容性加载：先尝试不带环境检查，失败时使用自定义对象
    try:
        if algo == 'SAC':
            model = SAC.load(model_path, env=None)
        elif algo == 'TD3':
            model = TD3.load(model_path, env=None)
        elif algo == 'PPO':
            model = PPO.load(model_path, env=None)
        else:
            raise ValueError(f"Unsupported algorithm: {algo}")
        print("Model loaded successfully without space checking")
    except Exception as e:
        print(f"Failed to load model without env: {e}")
        print("Trying to load model with custom_objects to fix action_space check...")
        try:
            if algo == 'SAC':
                model = SAC.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            elif algo == 'TD3':
                model = TD3.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            elif algo == 'PPO':
                model = PPO.load(model_path, env=env, custom_objects={'action_space': env.action_space})
            else:
                raise ValueError(f"Unsupported algorithm: {algo}")
            print("Model loaded successfully with custom_objects")
        except Exception as e2:
            print(f"Failed to load model with custom_objects: {e2}")
            raise e2
    
    # 手动设置环境，确保正确的环境检查
    model.set_env(env)
    return model


class ContinueTrainingThread(TrainingThread):
    """继续训练线程，继承自TrainingThread"""

    # 定义信号
    finished = QtCore.pyqtSignal()
    error_occurred = QtCore.pyqtSignal(str)

    def __init__(self, config_path, model_path, model_type, additional_steps):
        # 先不调用父类初始化，我们需要自定义初始化
        QtCore.QThread.__init__(self)
        print("init continue training thread")

        # 保存继续训练的参数
        self.config_path = config_path
        self.model_path = model_path
        self.model_type = model_type
        self.additional_steps = additional_steps
        self.original_timesteps = 0

        # 读取配置
        self.cfg = ConfigParser()
        try:
            self.cfg.read(config_path, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                self.cfg.read(config_path)
            except Exception as e:
                raise ValueError(f"Cannot read config file {config_path}: {e}")

        if not self.cfg.has_section('options'):
            raise ValueError(f"Missing [options] section in config file: {config_path}")

        env_name = self.cfg.get('options', 'env_name')
        self.project_name = env_name

        # 创建环境
        import gym
        import gym_env
        self.env = gym.make('airsim-env-v0')
        self.env.set_config(self.cfg)

        # 加载模型
        algo = self.cfg.get('options', 'algo')
        self.model = load_model_with_compatibility(algo, self.model_path, self.env)
        self.original_timesteps = self.model.num_timesteps
        print(f"Model loaded successfully. Current timesteps: {self.original_timesteps}")

        # 设置保存路径
        self.setup_save_paths()

        # 设置环境的模型引用
        self.env.model = self.model
        self.env.data_path = os.path.join(self.model_path_new, 'data')
        os.makedirs(self.env.data_path, exist_ok=True)

    def setup_save_paths(self):
        """设置保存路径"""
        timestamp = datetime.now().strftime('%Y_%m_%d_%H_%M')

        # 从配置中获取环境信息
        env_name = self.cfg.get('options', 'env_name')
        dynamic_name = self.cfg.get('options', 'dynamic_name')
        algo = self.cfg.get('options', 'algo')
        policy_name = self.cfg.get('options', 'policy_name')

        # 确定logs目录位置
        if os.path.exists('logs'):
            logs_base = 'logs'
        elif os.path.exists('../logs'):
            logs_base = '../logs'
        else:
            logs_base = os.path.join(os.path.dirname(__file__), '..', 'logs')
            os.makedirs(logs_base, exist_ok=True)

        # 创建环境特定的目录结构
        env_logs_dir = os.path.join(logs_base, env_name)
        os.makedirs(env_logs_dir, exist_ok=True)

        # 生成描述性的文件夹名称
        folder_name = f'{timestamp}_{dynamic_name}_{policy_name}_{algo}_continued'
        if self.model_type == 'final':
            folder_name += '_from_final'
        else:
            folder_name += '_from_checkpoint'

        self.model_path_new = os.path.join(env_logs_dir, folder_name)

        print(f"Environment: {env_name}")
        print(f"Creating new training directory: {self.model_path_new}")

        # 设置所有保存路径
        self.model_save_path = os.path.join(self.model_path_new, 'models')
        self.checkpoint_save_path = os.path.join(self.model_path_new, 'models', 'checkpoints')
        self.config_save_path = os.path.join(self.model_path_new, 'config')
        self.tb_log_path = os.path.join(self.model_path_new, 'tb_logs')

        # 创建所有必要的目录
        os.makedirs(self.model_save_path, exist_ok=True)
        os.makedirs(self.checkpoint_save_path, exist_ok=True)
        os.makedirs(self.config_save_path, exist_ok=True)
        os.makedirs(self.tb_log_path, exist_ok=True)

        # 保存配置文件到新目录（使用相同的格式化逻辑，去除中文注释）
        try:
            config_dest = os.path.join(self.config_save_path, 'config.ini')
            with open(config_dest, 'w', encoding='utf-8') as configfile:
                self.cfg.write(configfile)
            print(f"Configuration file saved to new directory (formatted, comments removed)")
        except Exception as e:
            print(f"Warning: Could not save config file: {e}")

        # 记录原始模型信息
        info_file = os.path.join(self.model_path_new, 'training_info.txt')
        with open(info_file, 'w') as f:
            f.write(f"Continue Training Information\n")
            f.write(f"=" * 40 + "\n")
            f.write(f"Original model: {self.model_path}\n")
            f.write(f"Model type: {self.model_type}\n")
            f.write(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Additional steps: {self.additional_steps}\n")
            f.write(f"Original timesteps: {self.original_timesteps}\n")
            f.write(f"Environment: {env_name}\n")
            f.write(f"Algorithm: {algo}\n")
            f.write(f"Policy: {policy_name}\n")
            f.write(f"Stability enhancements: Enabled\n")
            f.write(f"GUI monitoring: Enabled\n")
        print(f"Training info saved to: {info_file}")

        # 设置TensorBoard日志
        self.model.tensorboard_log = self.tb_log_path

    def run(self):
        """运行继续训练"""
        try:
            print(f"🚀 Starting continue training with GUI...")
            print(f"Original timesteps: {self.original_timesteps}")
            print(f"Additional steps: {self.additional_steps}")
            print(f"Target total timesteps: {self.original_timesteps + self.additional_steps}")

            # 创建检查点回调
            from stable_baselines3.common.callbacks import BaseCallback

            class ContinueTrainingCheckpointCallback(BaseCallback):
                def __init__(self, save_freq=5000, checkpoint_path=None, model_path=None, verbose=1):
                    super(ContinueTrainingCheckpointCallback, self).__init__(verbose)
                    self.save_freq = save_freq
                    self.checkpoint_path = checkpoint_path
                    self.model_path = model_path

                def _on_step(self) -> bool:
                    if self.n_calls % self.save_freq == 0:
                        checkpoint_name = f"checkpoint_{self.model.num_timesteps}_steps"
                        checkpoint_file = os.path.join(self.checkpoint_path, f"{checkpoint_name}.zip")
                        self.model.save(checkpoint_file)
                        if self.verbose > 0:
                            print(f"Step {self.model.num_timesteps}: Checkpoint saved to {checkpoint_file}")
                    return True

                def _on_training_end(self):
                    """训练结束时保存最终模型"""
                    final_model_path = os.path.join(self.model_path, 'model_sb3.zip')
                    self.model.save(final_model_path)
                    if self.verbose > 0:
                        print(f"Final model saved: {final_model_path}")

            # 创建检查点回调
            checkpoint_callback = ContinueTrainingCheckpointCallback(
                save_freq=5000,
                checkpoint_path=self.checkpoint_save_path,
                model_path=self.model_save_path,
                verbose=1
            )

            # 开始继续训练
            print('start continue training model')
            self.model.learn(
                total_timesteps=self.additional_steps,
                reset_num_timesteps=False,  # 重要：不重置时间步计数
                log_interval=1,
                callback=checkpoint_callback
            )

            print('continue training finished')
            print('model saved to: {}'.format(self.model_save_path))
            self.finished.emit()

        except Exception as e:
            print(f"❌ Continue training failed: {str(e)}")
            traceback.print_exc()
            self.error_occurred.emit(str(e))


def create_continue_training_session(model_path, model_type, config_file, additional_steps, logger):
    """创建继续训练会话（使用配置文件名）"""

    # 确定配置文件路径
    possible_config_paths = [
        f'configs/{config_file}.ini',     # 从项目根目录运行
        f'../configs/{config_file}.ini',  # 从scripts目录运行
        os.path.join(os.path.dirname(__file__), '..', 'configs', f'{config_file}.ini')  # 相对于脚本位置
    ]

    config_path = None
    for path in possible_config_paths:
        if os.path.exists(path):
            config_path = path
            break

    if config_path is None:
        raise FileNotFoundError(f"Config file '{config_file}.ini' not found")

    return create_continue_training_session_with_path(model_path, model_type, config_path, additional_steps, logger)


def create_continue_training_session_with_path(model_path, model_type, config_path, additional_steps, logger):
    """创建继续训练会话（使用配置文件路径）"""

    logger.info(f"Using config file: {os.path.abspath(config_path)}")

    # 创建Qt应用
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)

    # 创建继续训练线程
    training_thread = ContinueTrainingThread(config_path, model_path, model_type, additional_steps)

    # 创建GUI
    gui = TrainingUi(config_path)

    # 连接信号（信号在环境对象上）
    try:
        training_thread.env.action_signal.connect(gui.action_cb)
        training_thread.env.state_signal.connect(gui.state_cb)
        training_thread.env.attitude_signal.connect(gui.attitude_plot_cb)
        training_thread.env.reward_signal.connect(gui.reward_plot_cb)
        training_thread.env.pose_signal.connect(gui.traj_plot_cb)
        logger.info("GUI signals connected successfully")
    except Exception as e:
        logger.warning(f"Some GUI signals could not be connected: {e}")

    # 连接训练线程信号
    try:
        training_thread.finished.connect(lambda: logger.info("Continue training completed"))
        training_thread.error_occurred.connect(lambda error: logger.error(f"Training error: {error}"))
        logger.info("Training thread signals connected successfully")
    except Exception as e:
        logger.warning(f"Training thread signals could not be connected: {e}")

    return app, gui, training_thread


def main():
    logger = setup_error_logging()

    parser = get_parser()
    args = parser.parse_args()

    print("Continue Training with GUI Visualization")
    print("=" * 60)

    # 确定模型路径
    if args.model_path:
        model_path = args.model_path
        model_type = 'checkpoint' if 'checkpoint_' in model_path else 'final'
        if not os.path.exists(model_path):
            print(f"ERROR: Model file not found: {model_path}")
            return
    else:
        print("Searching for latest model...")
        model_path, model_type = find_best_model()
        if model_path is None:
            print("ERROR: No model found to continue training")
            return

    # 智能配置文件选择
    config_path = None
    config_source = "unknown"

    # 1. 首先尝试自动从模型目录找配置文件
    auto_config_path = find_config_for_model(model_path)
    if auto_config_path:
        config_path = auto_config_path
        config_source = "auto-detected from model directory"

    # 2. 如果用户指定了配置文件，优先使用用户指定的
    if args.config != 'config_Maze_SimpleMultirotor_2D_stable':  # 不是默认值
        print(f"🔧 User specified config: {args.config}")
        # 查找用户指定的配置文件
        possible_config_paths = [
            f'configs/{args.config}.ini',
            f'../configs/{args.config}.ini',
            os.path.join(os.path.dirname(__file__), '..', 'configs', f'{args.config}.ini')
        ]

        for path in possible_config_paths:
            if os.path.exists(path):
                config_path = path
                config_source = "user-specified"
                break

        if config_path is None or config_source == "auto-detected from model directory":
            print(f"⚠️  User-specified config '{args.config}' not found, using auto-detected config")

    # 3. 如果都没有找到，使用默认配置
    if config_path is None:
        print(f"⚠️  No config found, falling back to default: {args.config}")
        possible_config_paths = [
            f'configs/{args.config}.ini',
            f'../configs/{args.config}.ini',
            os.path.join(os.path.dirname(__file__), '..', 'configs', f'{args.config}.ini')
        ]

        for path in possible_config_paths:
            if os.path.exists(path):
                config_path = path
                config_source = "default fallback"
                break

    if config_path is None:
        print(f"ERROR: No config file found!")
        return

    additional_steps = args.additional_steps

    print(f"Continue Training Configuration:")
    print(f"   Model path: {model_path}")
    print(f"   Model type: {model_type}")
    print(f"   Config path: {config_path}")
    print(f"   Config source: {config_source}")
    print(f"   Additional steps: {additional_steps}")
    print(f"   GUI visualization: Enabled")
    print(f"   Stability features: Enabled")
    print("=" * 60)

    try:
        # 创建训练会话（直接传递config_path而不是config_file）
        app, gui, training_thread = create_continue_training_session_with_path(
            model_path, model_type, config_path, additional_steps, logger)

        # 显示GUI
        gui.show()

        # 启动训练线程
        training_thread.start()
        logger.info("Continue training thread started with GUI")

        # 运行应用
        exit_code = app.exec_()
        logger.info(f"Continue training completed with exit code: {exit_code}")

    except Exception as e:
        logger.error(f"Error during continue training: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
