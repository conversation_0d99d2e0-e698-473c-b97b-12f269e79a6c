"""
AirSim稳定性管理器
用于处理长时间训练过程中的AirSim连接稳定性问题
"""

import time
import logging
import airsim
from typing import Optional


class AirSimStabilityManager:
    """AirSim连接稳定性管理器"""
    
    def __init__(self, client: airsim.MultirotorClient, max_reconnect_attempts: int = 3):
        self.client = client
        self.max_reconnect_attempts = max_reconnect_attempts
        self.reconnect_count = 0
        self.last_successful_operation = time.time()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def check_connection_health(self) -> bool:
        """检查AirSim连接健康状态"""
        try:
            # 尝试ping AirSim服务器
            self.client.ping()
            self.last_successful_operation = time.time()
            return True
        except Exception as e:
            self.logger.warning(f"AirSim connection health check failed: {str(e)}")
            return False
    
    def reconnect_if_needed(self) -> bool:
        """如果需要则重新连接AirSim"""
        if self.check_connection_health():
            return True
            
        self.logger.info("Attempting to reconnect to AirSim...")
        
        for attempt in range(self.max_reconnect_attempts):
            try:
                # 重新确认连接
                self.client.confirmConnection()
                
                # 验证连接
                if self.check_connection_health():
                    self.logger.info(f"Successfully reconnected to AirSim (attempt {attempt + 1})")
                    self.reconnect_count += 1
                    return True
                    
            except Exception as e:
                self.logger.warning(f"Reconnection attempt {attempt + 1} failed: {str(e)}")
                time.sleep(1.0)  # 等待1秒后重试
        
        self.logger.error("Failed to reconnect to AirSim after maximum attempts")
        return False
    
    def safe_image_request(self, image_requests, max_retries: int = 3) -> Optional[list]:
        """安全的图像请求，带有重试机制"""
        for retry in range(max_retries):
            try:
                # 检查连接健康状态
                if not self.check_connection_health():
                    if not self.reconnect_if_needed():
                        continue
                
                # 执行图像请求
                responses = self.client.simGetImages(image_requests)
                
                # 验证响应
                if len(responses) > 0 and responses[0].width > 0:
                    return responses
                else:
                    self.logger.warning(f"Invalid image response (retry {retry + 1}/{max_retries})")
                    time.sleep(0.1)
                    
            except Exception as e:
                self.logger.warning(f"Image request failed (retry {retry + 1}/{max_retries}): {str(e)}")
                
                # 尝试重新连接
                if not self.reconnect_if_needed():
                    time.sleep(0.5)
        
        self.logger.error("Failed to get valid image response after maximum retries")
        return None
    
    def get_connection_stats(self) -> dict:
        """获取连接统计信息"""
        return {
            'reconnect_count': self.reconnect_count,
            'last_successful_operation': self.last_successful_operation,
            'time_since_last_success': time.time() - self.last_successful_operation
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.reconnect_count = 0
        self.last_successful_operation = time.time()


def create_stability_manager(client: airsim.MultirotorClient) -> AirSimStabilityManager:
    """创建稳定性管理器的工厂函数"""
    return AirSimStabilityManager(client)
