import torch as th
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import gym
from gym import spaces

# 复制CNN_GAP_new的实现
class CNN_GAP_new_Debug(nn.Module):
    def __init__(self, observation_space: gym.spaces.Box, features_dim: int = 256, state_feature_dim=0):
        super(CNN_GAP_new_Debug, self).__init__()
        print(f"初始化CNN_GAP_new:")
        print(f"  observation_space.shape: {observation_space.shape}")
        print(f"  features_dim: {features_dim}")
        print(f"  state_feature_dim: {state_feature_dim}")
        
        # 设置特征维度
        assert state_feature_dim > 0
        self.feature_num_state = state_feature_dim
        self.feature_num_cnn = features_dim - state_feature_dim
        self.feature_all = None
        
        print(f"  feature_num_state: {self.feature_num_state}")
        print(f"  feature_num_cnn: {self.feature_num_cnn}")

        # 网络层定义
        self.conv1 = nn.Conv2d(1, 8, 5, 2)  # kernel_size=5, stride=2
        self.conv2 = nn.Conv2d(8, 8, 5, 2)  # kernel_size=5, stride=2
        self.conv3 = nn.Conv2d(8, 8, 3, 2)  # kernel_size=3, stride=2
        self.pool = nn.MaxPool2d(2, 3)      # kernel_size=2, stride=3
        self.gap_layer = nn.AvgPool2d(kernel_size=(8, 10), stride=1)

    def forward(self, observations: th.Tensor) -> th.Tensor:
        print(f"\n=== CNN_GAP_new Forward Pass ===")
        print(f"输入observations shape: {observations.shape}")
        
        # 提取深度图像 (第一个通道)
        depth_img = observations[:, 0:1, :, :]
        print(f"depth_img shape: {depth_img.shape}")
        
        # 归一化到(-1, 1)
        depth_img_norm = (depth_img - 0.5) * 2
        print(f"depth_img_norm shape: {depth_img_norm.shape}")
        print(f"depth_img_norm range: [{depth_img_norm.min():.3f}, {depth_img_norm.max():.3f}]")

        # 卷积层1
        self.layer_1_out = F.relu(self.conv1(depth_img_norm))
        print(f"conv1 output shape: {self.layer_1_out.shape}")
        
        # 卷积层2
        self.layer_2_out = F.relu(self.conv2(self.layer_1_out))
        print(f"conv2 output shape: {self.layer_2_out.shape}")
        
        # 卷积层3
        self.layer_3_out = F.relu(self.conv3(self.layer_2_out))
        print(f"conv3 output shape: {self.layer_3_out.shape}")
        
        # 池化层
        self.layer_small = self.pool(self.layer_3_out)
        print(f"pool output shape: {self.layer_small.shape}")
        
        # 展平
        self.flatten = th.flatten(self.layer_small, start_dim=1)
        print(f"flatten output shape: {self.flatten.shape}")
        print(f"CNN特征维度: {self.flatten.shape[1]}")

        # 提取状态特征 (第二个通道的左上角)
        state_feature = observations[:, 1, 0, 0:self.feature_num_state]
        print(f"state_feature shape: {state_feature.shape}")
        print(f"state_feature values: {state_feature}")

        # 特征融合
        x = th.cat((self.flatten, state_feature), dim=1)
        print(f"最终输出 shape: {x.shape}")
        print(f"最终特征维度: {x.shape[1]}")
        
        self.feature_all = x
        return x

def test_cnn_gap_dimensions():
    """测试CNN_GAP_new的维度计算"""
    print("=" * 60)
    print("测试CNN_GAP_new维度计算")
    print("=" * 60)
    
    # 模拟配置
    screen_height = 80
    screen_width = 100
    channels = 2
    state_feature_dim = 2
    cnn_feature_num = 48  # 你设置的值
    features_dim = state_feature_dim + cnn_feature_num  # 2 + 48 = 50
    
    print(f"配置参数:")
    print(f"  screen_height: {screen_height}")
    print(f"  screen_width: {screen_width}")
    print(f"  channels: {channels}")
    print(f"  state_feature_dim: {state_feature_dim}")
    print(f"  cnn_feature_num: {cnn_feature_num}")
    print(f"  features_dim: {features_dim}")
    
    # 创建观察空间 (模拟VecTransposeImage后的格式)
    observation_space = spaces.Box(
        low=0, high=255, 
        shape=(channels, screen_height, screen_width),  # CHW格式
        dtype=np.uint8
    )
    
    # 创建网络
    network = CNN_GAP_new_Debug(
        observation_space=observation_space,
        features_dim=features_dim,
        state_feature_dim=state_feature_dim
    )
    
    # 创建测试输入
    batch_size = 1
    test_input = th.randn(batch_size, channels, screen_height, screen_width)
    
    # 模拟真实的观察数据
    # 第一个通道：深度图像 (0-255)
    test_input[:, 0, :, :] = th.randint(0, 256, (batch_size, screen_height, screen_width)).float()
    
    # 第二个通道：状态特征图像 (大部分为0，左上角有状态特征)
    test_input[:, 1, :, :] = 0
    test_input[:, 1, 0, 0] = 128.0  # distance_norm
    test_input[:, 1, 0, 1] = 64.0   # relative_yaw_norm
    
    print(f"\n测试输入:")
    print(f"  test_input shape: {test_input.shape}")
    print(f"  深度图像范围: [{test_input[:, 0, :, :].min():.1f}, {test_input[:, 0, :, :].max():.1f}]")
    print(f"  状态特征: [{test_input[:, 1, 0, 0].item():.1f}, {test_input[:, 1, 0, 1].item():.1f}]")
    
    # 前向传播
    with th.no_grad():
        output = network(test_input)
    
    print(f"\n=== 测试结果 ===")
    print(f"期望输出维度: {features_dim}")
    print(f"实际输出维度: {output.shape[1]}")
    print(f"维度差异: {output.shape[1] - features_dim}")
    
    return output.shape[1]

def calculate_conv_output_size(input_size, kernel_size, stride, padding=0):
    """计算卷积层输出尺寸"""
    return (input_size + 2 * padding - kernel_size) // stride + 1

def manual_dimension_calculation():
    """手动计算每层的输出维度"""
    print("\n" + "=" * 60)
    print("手动计算CNN_GAP_new各层维度")
    print("=" * 60)
    
    # 输入尺寸
    h, w = 80, 100
    print(f"输入尺寸: ({h}, {w})")
    
    # Conv1: Conv2d(1, 8, 5, 2)
    h1 = calculate_conv_output_size(h, 5, 2)
    w1 = calculate_conv_output_size(w, 5, 2)
    print(f"Conv1 输出: (8, {h1}, {w1})")
    
    # Conv2: Conv2d(8, 8, 5, 2)
    h2 = calculate_conv_output_size(h1, 5, 2)
    w2 = calculate_conv_output_size(w1, 5, 2)
    print(f"Conv2 输出: (8, {h2}, {w2})")
    
    # Conv3: Conv2d(8, 8, 3, 2)
    h3 = calculate_conv_output_size(h2, 3, 2)
    w3 = calculate_conv_output_size(w2, 3, 2)
    print(f"Conv3 输出: (8, {h3}, {w3})")
    
    # MaxPool2d(2, 3): kernel_size=2, stride=3
    h4 = calculate_conv_output_size(h3, 2, 3)
    w4 = calculate_conv_output_size(w3, 2, 3)
    print(f"Pool 输出: (8, {h4}, {w4})")
    
    # Flatten
    flatten_size = 8 * h4 * w4
    print(f"Flatten 输出: {flatten_size}")
    
    # 加上状态特征
    total_features = flatten_size + 2
    print(f"总特征数 (CNN + 状态): {total_features}")
    
    return flatten_size, total_features

if __name__ == "__main__":
    # 手动计算维度
    cnn_features, total_features = manual_dimension_calculation()
    
    # 实际测试
    actual_output_dim = test_cnn_gap_dimensions()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print(f"手动计算的CNN特征数: {cnn_features}")
    print(f"手动计算的总特征数: {total_features}")
    print(f"实际网络输出维度: {actual_output_dim}")
    print(f"配置文件应设置 cnn_feature_num = {cnn_features}")
    print(f"或者 features_dim = {total_features}")
