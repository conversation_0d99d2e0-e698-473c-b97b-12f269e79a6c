---
name: "\U0001F680Feature Request"
about: How to create an issue for requesting a feature
labels: enhancement
title: "[Feature Request] request title"
---

**Important Note: We do not do technical support, nor consulting** and don't answer personal questions per email.
Please post your question on the [RL Discord](https://discord.com/invite/xhfNqQv), [Reddit](https://www.reddit.com/r/reinforcementlearning/) or [Stack Overflow](https://stackoverflow.com/) in that case.


### 🚀 Feature

A clear and concise description of the feature proposal.

### Motivation

Please outline the motivation for the proposal.
Is your feature request related to a problem? e.g.,"I'm always frustrated when [...]".
If this is related to another GitHub issue, please link here too.

### Pitch

A clear and concise description of what you want to happen.

### Alternatives

A clear and concise description of any alternative solutions or features you've considered, if any.

### Additional context

Add any other context or screenshots about the feature request here.

### Checklist

- [ ] I have checked that there is no similar [issue](https://github.com/DLR-RM/stable-baselines3/issues) in the repo (**required**)


<!--- This Template is an edited version of the one from https://github.com/pytorch/pytorch -->
