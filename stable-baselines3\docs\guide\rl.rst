.. _rl:

================================
Reinforcement Learning Resources
================================


Stable-Baselines3 assumes that you already understand the basic concepts of Reinforcement Learning (RL).

However, if you want to learn about RL, there are several good resources to get started:

- `OpenAI Spinning Up <https://spinningup.openai.com/en/latest/>`_
- `<PERSON>'s course <http://www0.cs.ucl.ac.uk/staff/d.silver/web/Teaching.html>`_
- `<PERSON><PERSON>'s blog <https://lilianweng.github.io/lil-log/2018/04/08/policy-gradient-algorithms.html>`_
- `Berkeley's Deep RL Bootcamp <https://sites.google.com/view/deep-rl-bootcamp/lectures>`_
- `<PERSON>'s Deep Reinforcement Learning course <http://rail.eecs.berkeley.edu/deeprlcourse/>`_
- `More resources <https://github.com/dennybritz/reinforcement-learning>`_
