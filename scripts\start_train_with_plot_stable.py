import sys
import argparse
import time
import traceback
import os

from PyQt5 import QtWidgets

# from evaluate_td3 import evaluate
from utils.thread_train import TrainingThread
# from utils.thread_train_fixedwing import TrainingThread
from utils.ui_train import TrainingUi
from configparser import ConfigParser


def get_parser():
    parser = argparse.ArgumentParser(
        description="Stable training navigation model using TD3/SAC")
    parser.add_argument('-config', required=True,
                        help='config file name, such as config0925.ini', default='config_default.ini')
    parser.add_argument('-objective', required=True, help='training objective')

    return parser


def setup_error_logging():
    """设置错误日志记录"""
    import logging

    # 创建logs目录（如果不存在）
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 设置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training_errors.log')),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return logging.getLogger(__name__)


def validate_config_file(config_file):
    """验证配置文件是否存在且格式正确"""
    # 如果是相对路径，尝试从不同位置查找
    possible_paths = [
        config_file,  # 原始路径
        os.path.join('..', config_file),  # 从scripts目录向上一级
        os.path.join(os.path.dirname(__file__), '..', config_file),  # 相对于脚本位置
    ]

    actual_config_file = None
    for path in possible_paths:
        if os.path.exists(path):
            actual_config_file = path
            break

    if actual_config_file is None:
        raise FileNotFoundError(f"Config file not found in any of these locations: {possible_paths}")

    # 测试配置文件是否可以正确读取
    test_cfg = ConfigParser()
    try:
        # 尝试使用UTF-8编码读取
        test_cfg.read(actual_config_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            # 如果UTF-8失败，尝试使用系统默认编码
            test_cfg.read(actual_config_file)
        except Exception as e:
            raise ValueError(f"Cannot read config file {actual_config_file}: {e}")

    # 检查必要的section是否存在
    required_sections = ['options', 'environment']
    for section in required_sections:
        if not test_cfg.has_section(section):
            raise ValueError(f"Missing required section '{section}' in config file")

    # 检查必要的options
    required_options = ['env_name', 'dynamic_name', 'algo']
    for option in required_options:
        if not test_cfg.has_option('options', option):
            raise ValueError(f"Missing required option '{option}' in [options] section")

    return test_cfg, actual_config_file


def create_training_session(config_file, logger):
    """创建一个训练会话"""
    # 验证配置文件
    cfg, actual_config_file = validate_config_file(config_file)
    logger.info(f"Config file validation passed. Using: {actual_config_file}")

    # 检查是否已经有QApplication实例
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)
        logger.info("Created new QApplication instance")
    else:
        logger.info("Using existing QApplication instance")

    # 创建UI (使用实际找到的配置文件路径)
    gui = TrainingUi(actual_config_file)
    gui.show()
    logger.info("Training UI created and shown")

    # 创建训练线程 (使用实际找到的配置文件路径)
    training_thread = TrainingThread(actual_config_file)
    logger.info("Training thread created")

    # 连接信号
    training_thread.env.action_signal.connect(gui.action_cb)
    training_thread.env.state_signal.connect(gui.state_cb)
    training_thread.env.attitude_signal.connect(gui.attitude_plot_cb)
    training_thread.env.reward_signal.connect(gui.reward_plot_cb)
    training_thread.env.pose_signal.connect(gui.traj_plot_cb)
    logger.info("Signals connected")

    return app, gui, training_thread


def handle_airsim_error(logger, error_msg):
    """处理AirSim相关错误"""
    if "bad cast" in error_msg or "simGetImages" in error_msg:
        logger.warning("Detected AirSim RPC error, attempting recovery...")
        return True
    return False


def main():
    logger = setup_error_logging()

    # select your config file here
    #config_file = 'configs\config_NH_center_SimpleMultirotor_3D.ini'
    #config_file = 'configs/config_Maze_SimpleMultirotor_2D_stable.ini'
    config_file = 'configs\config_My_tree_1_SimpleMultirotor.ini'

    logger.info(f"Starting stable training with config: {config_file}")

    max_restart_attempts = 5  # 增加重试次数
    restart_count = 0
    consecutive_failures = 0

    while restart_count < max_restart_attempts:
        try:
            logger.info(f"Training attempt {restart_count + 1}/{max_restart_attempts}")

            # 创建训练会话
            app, gui, training_thread = create_training_session(config_file, logger)

            # 启动训练线程
            training_thread.start()
            logger.info("Training thread started")

            # 运行应用
            exit_code = app.exec_()

            logger.info(f"Training completed with exit code: {exit_code}")
            consecutive_failures = 0  # 重置连续失败计数
            break  # 成功完成，退出重试循环

        except Exception as e:
            restart_count += 1
            consecutive_failures += 1
            error_msg = str(e)

            logger.error(f"Training failed (attempt {restart_count}/{max_restart_attempts}): {error_msg}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            # 检查是否是AirSim相关错误
            is_airsim_error = handle_airsim_error(logger, error_msg)

            # 清理资源
            try:
                # 强制终止训练线程
                if 'training_thread' in locals() and training_thread.isRunning():
                    training_thread.terminate()
                    training_thread.wait(5000)  # 等待5秒

                app = QtWidgets.QApplication.instance()
                if app:
                    app.quit()
                    app.processEvents()
            except Exception as cleanup_error:
                logger.warning(f"Error during cleanup: {str(cleanup_error)}")

            if restart_count < max_restart_attempts:
                # 根据错误类型调整等待时间
                if is_airsim_error:
                    wait_time = 15 + (consecutive_failures * 5)  # AirSim错误等待更长时间
                    logger.info(f"AirSim error detected. Waiting {wait_time} seconds for recovery...")
                else:
                    wait_time = 10
                    logger.info(f"Waiting {wait_time} seconds before restart attempt {restart_count + 1}...")

                time.sleep(wait_time)
            else:
                logger.error("Maximum restart attempts reached. Training failed.")
                raise e

    logger.info("Training session completed")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print('System exit by user')
    except Exception as e:
        print(f'System exit due to error: {str(e)}')
        traceback.print_exc()
