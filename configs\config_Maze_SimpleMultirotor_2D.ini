[options]
env_name = SimpleAvoid
dynamic_name = Multirotor

navigation_3d = False
using_velocity_state = False
reward_type = reward_final

;depth, lgmd, vector
perception = depth

algo = SAC
total_timesteps = 150000

policy_name = CNN_GAP
net_arch = [64, 32, 16]
activation_function = tanh
cnn_feature_num = 72

keyboard_debug = False
generate_q_map = True
q_map_save_steps = 10000

use_wandb = False

[wandb]
name = Maze-2D-mlp-tanh-M3
notes = test

[environment]
max_depth_meters = 15
screen_height = 80
screen_width = 100

crash_distance = 2
accept_radius = 2

[multirotor]
dt = 0.1
acc_xy_max = 2.0
v_xy_max = 5
v_xy_min = 0.5
v_z_max = 2.0 
yaw_rate_max_deg = 30.0

; configs for DRL algorithms
[DRL]
gamma = 0.99
learning_rate = 1e-3
learning_starts = 2000
buffer_size = 50000
batch_size = 512
train_freq = 100
gradient_steps = 100
action_noise_sigma = 0.1
