"""
AirSim恢复管理器
专门处理AirSim训练过程中的连接问题和错误恢复
"""

import time
import logging
import airsim
import traceback
from typing import Optional, Callable


class AirSimRecoveryManager:
    """AirSim连接恢复管理器"""
    
    def __init__(self, client: airsim.MultirotorClient, max_recovery_attempts: int = 3):
        self.client = client
        self.max_recovery_attempts = max_recovery_attempts
        self.recovery_count = 0
        self.last_successful_operation = time.time()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def is_connection_healthy(self) -> bool:
        """检查AirSim连接是否健康"""
        try:
            # 尝试ping AirSim服务器
            self.client.ping()
            self.last_successful_operation = time.time()
            return True
        except Exception as e:
            self.logger.warning(f"AirSim connection health check failed: {str(e)}")
            return False
    
    def emergency_reset(self) -> bool:
        """紧急重置AirSim环境"""
        try:
            self.logger.info("Performing emergency reset...")
            
            # 重置AirSim环境
            self.client.reset()
            time.sleep(2.0)
            
            # 重新确认连接
            self.client.confirmConnection()
            time.sleep(1.0)
            
            # 验证连接
            if self.is_connection_healthy():
                self.logger.info("Emergency reset successful")
                return True
            else:
                self.logger.warning("Emergency reset failed - connection still unhealthy")
                return False
                
        except Exception as e:
            self.logger.error(f"Emergency reset failed: {str(e)}")
            return False
    
    def recover_from_error(self, error_msg: str) -> bool:
        """从错误中恢复"""
        self.logger.info(f"Attempting to recover from error: {error_msg}")
        
        # 检查错误类型
        if "bad cast" in error_msg or "simGetImages" in error_msg:
            return self._recover_from_rpc_error()
        elif "connection" in error_msg.lower():
            return self._recover_from_connection_error()
        else:
            return self._generic_recovery()
    
    def _recover_from_rpc_error(self) -> bool:
        """从RPC错误中恢复"""
        self.logger.info("Recovering from RPC error...")
        
        for attempt in range(self.max_recovery_attempts):
            try:
                # 等待一段时间让AirSim稳定
                time.sleep(1.0 + attempt)
                
                # 重新确认连接
                self.client.confirmConnection()
                time.sleep(0.5)
                
                # 测试图像请求
                test_response = self.client.simGetImages([
                    airsim.ImageRequest("0", airsim.ImageType.DepthVis, True)
                ])
                
                if len(test_response) > 0 and test_response[0].width > 0:
                    self.logger.info(f"RPC recovery successful (attempt {attempt + 1})")
                    return True
                    
            except Exception as e:
                self.logger.warning(f"RPC recovery attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.max_recovery_attempts - 1:
                    # 最后一次尝试紧急重置
                    return self.emergency_reset()
                time.sleep(2.0)
        
        return False
    
    def _recover_from_connection_error(self) -> bool:
        """从连接错误中恢复"""
        self.logger.info("Recovering from connection error...")
        
        for attempt in range(self.max_recovery_attempts):
            try:
                # 重新建立连接
                self.client.confirmConnection()
                time.sleep(1.0)
                
                # 验证连接
                if self.is_connection_healthy():
                    self.logger.info(f"Connection recovery successful (attempt {attempt + 1})")
                    return True
                    
            except Exception as e:
                self.logger.warning(f"Connection recovery attempt {attempt + 1} failed: {str(e)}")
                time.sleep(2.0 + attempt)
        
        # 如果常规恢复失败，尝试紧急重置
        return self.emergency_reset()
    
    def _generic_recovery(self) -> bool:
        """通用恢复方法"""
        self.logger.info("Performing generic recovery...")
        
        try:
            # 等待一段时间
            time.sleep(2.0)
            
            # 重新确认连接
            self.client.confirmConnection()
            time.sleep(1.0)
            
            # 验证连接
            if self.is_connection_healthy():
                self.logger.info("Generic recovery successful")
                return True
            else:
                # 尝试紧急重置
                return self.emergency_reset()
                
        except Exception as e:
            self.logger.error(f"Generic recovery failed: {str(e)}")
            return self.emergency_reset()
    
    def safe_execute(self, operation: Callable, operation_name: str = "operation", max_retries: int = 3):
        """安全执行操作，带有自动恢复机制"""
        for retry in range(max_retries):
            try:
                # 检查连接健康状态
                if not self.is_connection_healthy():
                    if not self.recover_from_error("connection check failed"):
                        continue
                
                # 执行操作
                result = operation()
                self.last_successful_operation = time.time()
                return result
                
            except Exception as e:
                error_msg = str(e)
                self.logger.warning(f"{operation_name} failed (retry {retry + 1}/{max_retries}): {error_msg}")
                
                if retry < max_retries - 1:
                    # 尝试恢复
                    if self.recover_from_error(error_msg):
                        continue
                    else:
                        time.sleep(1.0)
                else:
                    # 最后一次重试失败
                    self.logger.error(f"{operation_name} failed after {max_retries} retries")
                    raise e
        
        raise Exception(f"Failed to execute {operation_name} after recovery attempts")
    
    def get_recovery_stats(self) -> dict:
        """获取恢复统计信息"""
        return {
            'recovery_count': self.recovery_count,
            'last_successful_operation': self.last_successful_operation,
            'time_since_last_success': time.time() - self.last_successful_operation
        }


def create_recovery_manager(client: airsim.MultirotorClient) -> AirSimRecoveryManager:
    """创建恢复管理器的工厂函数"""
    return AirSimRecoveryManager(client)
